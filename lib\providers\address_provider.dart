import 'package:flutter/foundation.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/address/address.dart';
import 'package:rideoon/models/address/address_request.dart';
import 'package:rideoon/models/api_response.dart';

/// Provider for managing address state across the application
///
/// This provider handles:
/// - Loading saved addresses
/// - Creating new addresses
/// - Updating existing addresses
/// - Deleting addresses
/// - Notifying listeners when address data changes
class AddressProvider extends ChangeNotifier {
  List<Map<String, dynamic>> _addresses = [];
  bool _isLoading = false;
  String? _error;

  /// Get all addresses
  List<Map<String, dynamic>> get addresses => List.unmodifiable(_addresses);

  /// Get addresses filtered by type
  List<Map<String, dynamic>> getAddressesByType(String type) {
    return _addresses.where((addr) => 
      addr['type'] == type || addr['type'] == 'address'
    ).toList();
  }

  /// Get pickup addresses
  List<Map<String, dynamic>> get pickupAddresses => getAddressesByType('pickup');

  /// Get receiver addresses
  List<Map<String, dynamic>> get receiverAddresses => getAddressesByType('receiver');

  /// Check if currently loading
  bool get isLoading => _isLoading;

  /// Get current error message
  String? get error => _error;

  /// Load all saved addresses from the API
  Future<void> loadAddresses() async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken != null) {
        final addresses = await AddressService.getSavedAddresses(authToken: authToken);
        
        _addresses = addresses.map((addr) => {
          ...addr,
          'icon': _getAddressIcon(addr['type']),
          'title': _getAddressTitle(addr),
        }).toList();
      } else {
        _addresses = [];
      }
    } catch (e) {
      _setError('Failed to load addresses: $e');
      _addresses = [];
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new address
  Future<bool> createAddress(AddressRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to save address');
        return false;
      }

      final response = await AddressService.createAddress(request, authToken: authToken);
      
      if (response.success) {
        // Reload addresses to get the updated list
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to create address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to create address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing address
  Future<bool> updateAddress(String addressUuid, AddressRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to update address');
        return false;
      }

      final response = await AddressService.updateAddress(addressUuid, request, authToken: authToken);
      
      if (response.success) {
        // Reload addresses to get the updated list
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to update address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to update address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an address
  Future<bool> deleteAddress(String addressUuid) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to delete address');
        return false;
      }

      final response = await AddressService.deleteAddress(addressUuid, authToken: authToken);
      
      if (response.success) {
        // Remove the address from local list immediately for better UX
        _addresses.removeWhere((addr) => addr['id'] == addressUuid);
        notifyListeners();
        
        // Then reload to ensure consistency
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to delete address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to delete address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh addresses (alias for loadAddresses for clarity)
  Future<void> refresh() async {
    await loadAddresses();
  }

  /// Clear all addresses (for logout scenarios)
  void clearAddresses() {
    _addresses.clear();
    _clearError();
    notifyListeners();
  }



  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Helper method to set error
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Helper method to clear error
  void _clearError() {
    _error = null;
  }

  /// Get icon for address type
  String _getAddressIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'pickup':
        return 'pickup';
      case 'receiver':
      case 'delivery':
        return 'delivery';
      case 'home':
        return 'home';
      case 'work':
      case 'office':
        return 'work';
      default:
        return 'location';
    }
  }

  /// Get title for address
  String _getAddressTitle(Map<String, dynamic> address) {
    final name = address['name'] ?? address['addressName'];
    final street = address['street'] ?? address['fullAddress'] ?? address['address'];
    
    if (name != null && name.toString().isNotEmpty) {
      return name.toString();
    } else if (street != null && street.toString().isNotEmpty) {
      final streetStr = street.toString();
      // Return first 30 characters of street address
      return streetStr.length > 30 ? '${streetStr.substring(0, 30)}...' : streetStr;
    }
    
    return 'Saved Address';
  }
}
