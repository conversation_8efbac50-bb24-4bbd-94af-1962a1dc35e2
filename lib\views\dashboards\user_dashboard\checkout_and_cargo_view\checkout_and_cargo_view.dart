import 'dart:io';
import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/payment_summary.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:rideoon/views/custom_widgets/floating_orders_button.dart';
import 'package:rideoon/services/order_service.dart';
import 'package:rideoon/views/dashboards/user_dashboard/account_view/customer_support_view.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Checkout and Cargo view page for user dashboard
///
/// This page contains the checkout process and cargo management functionality,
/// including payment summary, cargo items list, and order management options.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - Payment summary with breakdown of costs
/// - Cargo items management with add/edit/delete functionality
/// - Order duplication and delivery method switching
/// - Consistent styling with app theme
/// - Clean UI without debug info following home_view patterns
class CheckoutAndCargoView extends StatefulWidget {
  /// Callback function for navigation actions
  final VoidCallback? onNavigateBack;

  /// Callback function to navigate to home after successful checkout
  final VoidCallback? onNavigateToHome;

  /// Callback function to navigate back to send package view
  final VoidCallback? onNavigateToSendPackage;

  /// Initial payment data
  final PaymentSummaryData? initialPaymentData;

  /// Initial cargo items
  final List<CargoItem>? initialCargoItems;

  /// Package data from send package view
  final Map<String, dynamic>? packageData;

  const CheckoutAndCargoView({
    super.key,
    this.onNavigateBack,
    this.onNavigateToHome,
    this.onNavigateToSendPackage,
    this.initialPaymentData,
    this.initialCargoItems,
    this.packageData,
  });

  @override
  State<CheckoutAndCargoView> createState() => _CheckoutAndCargoViewState();
}

class _CheckoutAndCargoViewState extends State<CheckoutAndCargoView> {
  late PaymentSummaryData _paymentData;
  late List<CargoItem> _cargoItems;
  bool _isProcessingPayment = false;
  int _orderCount = 0;

  // Package data from send package view
  Map<String, dynamic> _packageData = {};
  String _selectedCategory = 'General';

  // Available categories for cargo items
  static const List<String> _availableCategories = [
    'Electronics',
    'Computer Accessories',
    'Clothing & Fashion',
    'Books & Documents',
    'Food & Beverages',
    'Home & Garden',
    'Sports & Outdoors',
    'Health & Beauty',
    'Toys & Games',
    'Automotive',
    'Jewelry & Accessories',
    'Art & Crafts',
    'Musical Instruments',
    'Office Supplies',
    'General',
  ];

  @override
  void initState() {
    super.initState();
    _initializeData();
    _loadOrderCount();
  }

  void _initializeData() {
    // Initialize package data from send package view
    _packageData = widget.packageData ?? {};

    // Extract category from package data
    if (_packageData.isNotEmpty && _packageData['packageDetails'] != null) {
      final packageDetails = _packageData['packageDetails'];
      if (packageDetails is Map<String, dynamic>) {
        _selectedCategory = packageDetails['category'] ?? 'General';
      } else if (packageDetails is Map) {
        _selectedCategory = packageDetails['category']?.toString() ?? 'General';
      }
    }

    // Initialize payment data with sample or provided data
    _paymentData = widget.initialPaymentData ?? const PaymentSummaryData(
      shippingCost: 2600,
      vat: 0,
      insurance: 0,
      pickupCharge: 0,
      isInsuranceFree: true,
      isPickupFree: true,
    );

    // Initialize cargo items with provided data only (no default items)
    _cargoItems = widget.initialCargoItems ?? [];
  }

  /// Load order count for floating button badge
  Future<void> _loadOrderCount() async {
    try {
      final count = await OrderService.getTotalOrderCount();
      if (mounted) {
        setState(() {
          _orderCount = count;
        });
      }
    } catch (e) {
      print('Error loading order count: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        children: [
          SafeArea(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 7% gap from top of screen (following home_view pattern)
                  SizedBox(height: MediaQuery.of(context).size.height * 0.07),

                  // Header
                  _buildHeader(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Delivery Summary
                  _buildDeliverySummary(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Pickup Summary
                  _buildPickupSummary(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Cargo Items with Add Button
                  _buildCargoSection(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Payment Summary (moved below cargo)
                  PaymentSummary(
                    paymentData: _paymentData,
                    title: 'Payment Summary',
                    showShadow: true,
                  ),

                  SizedBox(height: _getSpacing(context, 32)),

                  // Action buttons
                  _buildActionButtons(context),

                  SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom nav
                ],
              ),
            ),
          ),

          // Floating Orders Button
          FloatingOrdersButton(
            onTap: _handleViewAllOrders,
            orderCount: _orderCount,
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Row(
        children: [
          // Back button (if navigation callback provided)
          if (widget.onNavigateBack != null) ...[
            GestureDetector(
              onTap: widget.onNavigateBack,
              child: Container(
                width: _getIconSize(context, 40),
                height: _getIconSize(context, 40),
                decoration: BoxDecoration(
                  color: AppColors.black.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                ),
                child: Icon(
                  Icons.arrow_back_ios_new,
                  size: _getIconSize(context, 20),
                  color: AppColors.black,
                ),
              ),
            ),
            SizedBox(width: _getSpacing(context, 16)),
          ],

          // Title
          Expanded(
            child: Text(
              'Cargo and Orders',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getTitleFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
                letterSpacing: -0.5,
              ),
            ),
          ),

          // Optional action button (e.g., help or settings)
          GestureDetector(
            onTap: _handleHelpTap,
            child: Container(
              width: _getIconSize(context, 40),
              height: _getIconSize(context, 40),
              decoration: BoxDecoration(
                color: AppColors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              ),
              child: Icon(
                Icons.help_outline,
                size: _getIconSize(context, 20),
                color: AppColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliverySummary(BuildContext context) {
    final deliveryType = _packageData['deliveryType'] ?? 'instant';
    final packageDetailsRaw = _packageData['packageDetails'];
    final packageDetails = packageDetailsRaw is Map<String, dynamic>
        ? packageDetailsRaw
        : (packageDetailsRaw is Map ? Map<String, dynamic>.from(packageDetailsRaw) : <String, dynamic>{});

    return Container(
      margin: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 18)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: _getSpacing(context, 8),
            offset: Offset(0, _getSpacing(context, 2)),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                deliveryType == 'instant' ? Icons.flash_on : Icons.schedule,
                color: AppColors.primary,
                size: _getIconSize(context, 24),
              ),
              SizedBox(width: _getSpacing(context, 12)),
              Expanded(
                child: Text(
                  'Delivery Summary',
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getTitleFontSize(context) - 2,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              GestureDetector(
                onTap: _handleSwitchDeliveryMethod,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: _getSpacing(context, 12),
                    vertical: _getSpacing(context, 6),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                  ),
                  child: Text(
                    'Switch',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontSize: _getBodyFontSize(context),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 16)),

          _buildSummaryRow(context, 'Delivery Type', deliveryType == 'instant' ? 'Instant Delivery' : 'Scheduled Delivery'),

          if (deliveryType == 'scheduled' && packageDetails['pickupDate'] != null) ...[
            SizedBox(height: _getSpacing(context, 8)),
            _buildSummaryRow(context, 'Pickup Date', _formatDate(packageDetails['pickupDate'])),
          ],

          if (deliveryType == 'scheduled' && packageDetails['deliveryDate'] != null) ...[
            SizedBox(height: _getSpacing(context, 8)),
            _buildSummaryRow(context, 'Delivery Date', _formatDate(packageDetails['deliveryDate'])),
          ],


        ],
      ),
    );
  }

  Widget _buildPickupSummary(BuildContext context) {
    final pickupDataRaw = _packageData['pickupDetails'];
    final receiverDataRaw = _packageData['receiverDetails'];

    final pickupData = pickupDataRaw is Map<String, dynamic>
        ? pickupDataRaw
        : (pickupDataRaw is Map ? Map<String, dynamic>.from(pickupDataRaw) : <String, dynamic>{});

    final receiverData = receiverDataRaw is Map<String, dynamic>
        ? receiverDataRaw
        : (receiverDataRaw is Map ? Map<String, dynamic>.from(receiverDataRaw) : <String, dynamic>{});

    return Container(
      margin: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 18)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: _getSpacing(context, 8),
            offset: Offset(0, _getSpacing(context, 2)),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppColors.primary,
                size: _getIconSize(context, 24),
              ),
              SizedBox(width: _getSpacing(context, 12)),
              Text(
                'Location Summary',
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: _getTitleFontSize(context) - 2,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Pickup Location
          if (pickupData.isNotEmpty) ...[
            _buildLocationSection(context, 'Pickup Location', pickupData),
            SizedBox(height: _getSpacing(context, 16)),
          ],

          // Delivery Location
          if (receiverData.isNotEmpty) ...[
            _buildLocationSection(context, 'Delivery Location', receiverData),
          ],

          if (pickupData.isEmpty && receiverData.isEmpty) ...[
            Text(
              'No location data available',
              style: TextStyle(
                color: AppColors.black.withValues(alpha: 0.6),
                fontSize: _getBodyFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCargoSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with Add button
          Row(
            children: [
              Icon(
                Icons.inventory_2,
                color: AppColors.primary,
                size: _getIconSize(context, 24),
              ),
              SizedBox(width: _getSpacing(context, 12)),
              Expanded(
                child: Text(
                  'Cargo Items (${_cargoItems.length})',
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getTitleFontSize(context) - 2,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              GestureDetector(
                onTap: _handleAddCargo,
                child: Container(
                  width: _getIconSize(context, 40),
                  height: _getIconSize(context, 40),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                  ),
                  child: Icon(
                    Icons.add,
                    color: AppColors.white,
                    size: _getIconSize(context, 24),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Cargo Items
          if (_cargoItems.isNotEmpty) ...[
            AddCargo(
              cargoItems: _cargoItems,
              onSwitchDeliveryMethod: _handleSwitchDeliveryMethod,
              onDuplicateOrder: _handleDuplicateOrder,
              onEditItem: _handleEditCargoItem,
              onDeleteItem: _handleDeleteCargoItem,
              showShadow: true,
              showActionButtons: true,
            ),
          ] else ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(_getSpacing(context, 32)),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 18)),
                border: Border.all(
                  color: AppColors.black.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: _getIconSize(context, 48),
                    color: AppColors.black.withValues(alpha: 0.4),
                  ),
                  SizedBox(height: _getSpacing(context, 12)),
                  Text(
                    'No cargo items added',
                    style: TextStyle(
                      color: AppColors.black.withValues(alpha: 0.6),
                      fontSize: _getBodyFontSize(context),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: _getSpacing(context, 4)),
                  Text(
                    'Tap the + button to add items',
                    style: TextStyle(
                      color: AppColors.black.withValues(alpha: 0.5),
                      fontSize: _getBodyFontSize(context) - 2,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        children: [
          // Primary action button (e.g., Proceed to Payment)
          SizedBox(
            width: double.infinity,
            height: _getButtonHeight(context),
            child: ElevatedButton(
              onPressed: _isProcessingPayment ? null : _handleProceedToPayment,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                elevation: 0,
              ),
              child: _isProcessingPayment
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Proceed to Checkout',
                      style: TextStyle(
                        fontSize: _getButtonFontSize(context),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),

          SizedBox(height: _getSpacing(context, 12)),

          // Secondary action button (e.g., Save as Draft)
          SizedBox(
            width: double.infinity,
            height: _getButtonHeight(context),
            child: OutlinedButton(
              onPressed: _handleSaveAsDraft,
              style: OutlinedButton.styleFrom(
                backgroundColor: AppColors.white,
                foregroundColor: AppColors.primary,
                side: BorderSide(
                  width: 1.5,
                  color: AppColors.primary,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                elevation: 0,
              ),
              child: Text(
                'Save as Draft',
                style: TextStyle(
                  fontSize: _getButtonFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Widget _buildSummaryRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppColors.black.withValues(alpha: 0.7),
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w400,
          ),
        ),
        Flexible(
          child: Text(
            value,
            style: TextStyle(
              color: AppColors.black,
              fontSize: _getBodyFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildLocationSection(BuildContext context, String title, Map<String, dynamic> locationData) {
    final isPickup = title == 'Pickup Location';
    final iconData = isPickup ? Icons.send : Icons.location_on;
    final iconColor = isPickup ? AppColors.primary : AppColors.success;

    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: (isPickup ? AppColors.primary : AppColors.success).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: (isPickup ? AppColors.primary : AppColors.success).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Container(
                width: _getIconSize(context, 32),
                height: _getIconSize(context, 32),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                ),
                child: Icon(
                  iconData,
                  color: iconColor,
                  size: _getIconSize(context, 18),
                ),
              ),
              SizedBox(width: _getSpacing(context, 12)),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context) + 1,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 12)),

          // Location details
          Column(
            children: [
              // For pickup data - use senderName and fullAddress
              if (isPickup) ...[
                if (locationData['senderName'] != null)
                  _buildEnhancedSummaryRow(context, 'Sender', locationData['senderName'], Icons.person),
                if (locationData['fullAddress'] != null) ...[
                  SizedBox(height: _getSpacing(context, 8)),
                  _buildEnhancedSummaryRow(context, 'Address', locationData['fullAddress'], Icons.home),
                ],
                if (locationData['landmark'] != null) ...[
                  SizedBox(height: _getSpacing(context, 8)),
                  _buildEnhancedSummaryRow(context, 'Landmark', locationData['landmark'], Icons.place),
                ],
              ],
              // For receiver data - use name and address
              if (!isPickup) ...[
                if (locationData['name'] != null)
                  _buildEnhancedSummaryRow(context, 'Receiver', locationData['name'], Icons.person),
                if (locationData['address'] != null) ...[
                  SizedBox(height: _getSpacing(context, 8)),
                  _buildEnhancedSummaryRow(context, 'Address', locationData['address'], Icons.home),
                ],
              ],
              // Common fields for both
              if (locationData['state'] != null) ...[
                SizedBox(height: _getSpacing(context, 8)),
                _buildEnhancedSummaryRow(context, 'State', locationData['state'], Icons.map),
              ],
              if (locationData['phone'] != null) ...[
                SizedBox(height: _getSpacing(context, 8)),
                _buildEnhancedSummaryRow(context, 'Phone', locationData['phone'], Icons.phone),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedSummaryRow(BuildContext context, String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: _getIconSize(context, 24),
          height: _getIconSize(context, 24),
          decoration: BoxDecoration(
            color: AppColors.black.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 6)),
          ),
          child: Icon(
            icon,
            color: AppColors.black.withValues(alpha: 0.6),
            size: _getIconSize(context, 14),
          ),
        ),
        SizedBox(width: _getSpacing(context, 12)),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: AppColors.black.withValues(alpha: 0.6),
                  fontSize: _getBodyFontSize(context) - 1,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: _getSpacing(context, 2)),
              Text(
                value,
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: _getBodyFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'Not set';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch - very small text
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet - larger text
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  // Event handlers
  void _handleSwitchDeliveryMethod() {
    // Navigate back to send package view without clearing checkout data
    if (widget.onNavigateToSendPackage != null) {
      widget.onNavigateToSendPackage!();
    } else {
      Navigator.of(context).pop();
    }
  }

  void _handleAddCargo() {
    _navigateToAddCargoItems();
  }

  void _navigateToAddCargoItems() async {
    // Navigate to add cargo items page using root navigator
    final result = await Navigator.of(context, rootNavigator: true).pushNamed(
      '/add-cargo-items',
      arguments: {
        'existingItems': _cargoItems,
        'packageData': _packageData,
      },
    );

    // Handle returned cargo items
    if (result != null && result is List<CargoItem>) {
      setState(() {
        _cargoItems = result;
      });
      Toast.success('Cargo items updated');
    }
  }

  void _handleEditCargoItem(int index) {
    if (index < _cargoItems.length) {
      _showEditCargoDialog(_cargoItems[index], index);
    }
  }

  void _handleDeleteCargoItem(int index) {
    if (index < _cargoItems.length) {
      setState(() {
        _cargoItems.removeAt(index);
      });

      Toast.success('Cargo item removed');
    }
  }

  void _showEditCargoDialog(CargoItem item, int index) {
    _showCargoDialog(item, index);
  }

  void _showCargoDialog(CargoItem? existingItem, int index) {
    final TextEditingController itemNameController = TextEditingController(
      text: existingItem?.itemName ?? '',
    );
    final TextEditingController itemTypeController = TextEditingController(
      text: existingItem?.itemType ?? '',
    );
    final TextEditingController weightController = TextEditingController(
      text: existingItem?.weight.toString() ?? '',
    );
    final TextEditingController quantityController = TextEditingController(
      text: existingItem?.quantity.toString() ?? '',
    );
    String selectedCategory = existingItem?.category ?? _selectedCategory;
    ItemDurability selectedDurability = existingItem?.durability ?? ItemDurability.average;
    List<String> selectedImages = List<String>.from(existingItem?.imagePaths ?? []);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: Offset(0, 8),
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        existingItem != null ? 'Edit Cargo Item' : 'Add Cargo Item',
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 18,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        color: AppColors.white,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Form content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Item Name
                      Text(
                        'Item Name',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8),
                      TextField(
                        controller: itemNameController,
                        decoration: InputDecoration(
                          hintText: 'Enter item name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        ),
                      ),

                      SizedBox(height: 16),

                      // Category
                      Text(
                        'Category',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8),
                      GestureDetector(
                        onTap: () => _showCategoryBottomSheet(context, selectedCategory, (String newCategory) {
                          setState(() {
                            selectedCategory = newCategory;
                          });
                        }),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.black.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                selectedCategory,
                                style: TextStyle(
                                  color: AppColors.black,
                                  fontSize: 14,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Icon(
                                Icons.keyboard_arrow_down,
                                color: AppColors.black.withValues(alpha: 0.6),
                                size: 20,
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(height: 16),

                      // Item Type
                      Text(
                        'Item Type',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8),
                      TextField(
                        controller: itemTypeController,
                        decoration: InputDecoration(
                          hintText: 'Enter item type',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        ),
                      ),

                      SizedBox(height: 16),

                      // Weight and Quantity in a row
                      Row(
                        children: [
                          // Weight
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Weight (kg)',
                                  style: TextStyle(
                                    color: AppColors.black,
                                    fontSize: 14,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: 8),
                                TextField(
                                  controller: weightController,
                                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                                  decoration: InputDecoration(
                                    hintText: '0.0',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(width: 16),

                          // Quantity
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Quantity',
                                  style: TextStyle(
                                    color: AppColors.black,
                                    fontSize: 14,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: 8),
                                TextField(
                                  controller: quantityController,
                                  keyboardType: TextInputType.number,
                                  decoration: InputDecoration(
                                    hintText: '1',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 16),

                      // Durability
                      Text(
                        'Durability',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8),
                      GestureDetector(
                        onTap: () => _showDurabilityBottomSheet(context, selectedDurability, (ItemDurability newDurability) {
                          setState(() {
                            selectedDurability = newDurability;
                          });
                        }),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.black.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                selectedDurability.displayName,
                                style: TextStyle(
                                  color: AppColors.black,
                                  fontSize: 14,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Icon(
                                Icons.keyboard_arrow_down,
                                color: AppColors.black.withValues(alpha: 0.6),
                                size: 20,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 16),

              // Images Section
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Item Images (Optional)',
                      style: TextStyle(
                        color: AppColors.black,
                        fontSize: 14,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8),
                    _buildImageSection(context, selectedImages, (List<String> images) {
                      setState(() {
                        selectedImages = images;
                      });
                    }),
                  ],
                ),
              ),

              // Action buttons
              Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: BorderSide(color: AppColors.primary),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          final itemName = itemNameController.text.trim();
                          final itemType = itemTypeController.text.trim();
                          final weightText = weightController.text.trim();
                          final quantityText = quantityController.text.trim();

                          // Validation
                          if (itemName.isEmpty || itemType.isEmpty || weightText.isEmpty || quantityText.isEmpty) {
                            Toast.error('Please fill in all fields');
                            return;
                          }

                          // Parse weight and quantity
                          final weight = double.tryParse(weightText);
                          final quantity = int.tryParse(quantityText);

                          if (weight == null || weight <= 0) {
                            Toast.error('Please enter a valid weight');
                            return;
                          }

                          if (quantity == null || quantity <= 0) {
                            Toast.error('Please enter a valid quantity');
                            return;
                          }

                          final newItem = CargoItem(
                            itemName: itemName,
                            category: selectedCategory,
                            itemType: itemType,
                            weight: weight,
                            quantity: quantity,
                            durability: selectedDurability,
                            imagePaths: selectedImages,
                            canEdit: true,
                            canDelete: true,
                          );

                          // Close dialog first
                          Navigator.of(context).pop();

                          // Update the main widget state
                          this.setState(() {
                            if (existingItem != null && index >= 0) {
                              _cargoItems[index] = newItem;
                              print('Updated cargo item at index $index. Total items: ${_cargoItems.length}');
                            } else {
                              _cargoItems.add(newItem);
                              print('Added new cargo item. Total items: ${_cargoItems.length}');
                            }
                          });

                          Toast.success(existingItem != null ? 'Cargo item updated' : 'Cargo item added');
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          existingItem != null ? 'Update' : 'Add',
                          style: TextStyle(
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    )
    );
  }

  void _showDurabilityBottomSheet(BuildContext context, ItemDurability currentDurability, Function(ItemDurability) onDurabilitySelected) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Select Durability',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: 18,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        color: AppColors.black.withValues(alpha: 0.6),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Durability options
              ...ItemDurability.values.map((durability) {
                final isSelected = durability == currentDurability;
                return GestureDetector(
                  onTap: () {
                    onDurabilitySelected(durability);
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.transparent,
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getDurabilityColor(durability).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: _getDurabilityColor(durability),
                              width: 2,
                            ),
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                durability.displayName,
                                style: TextStyle(
                                  color: AppColors.black,
                                  fontSize: 16,
                                  fontFamily: 'Poppins',
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: 2),
                              Text(
                                _getDurabilityDescription(durability),
                                style: TextStyle(
                                  color: AppColors.black.withValues(alpha: 0.6),
                                  fontSize: 12,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: AppColors.primary,
                            size: 20,
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),

              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  void _showCategoryBottomSheet(BuildContext context, String currentCategory, Function(String) onCategorySelected) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Select Category',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: 18,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        color: AppColors.black.withValues(alpha: 0.6),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Categories list
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _availableCategories.length,
                  itemBuilder: (context, index) {
                    final category = _availableCategories[index];
                    final isSelected = category == currentCategory;

                    return GestureDetector(
                      onTap: () {
                        onCategorySelected(category);
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        decoration: BoxDecoration(
                          color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.transparent,
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _getCategoryIcon(category),
                              color: isSelected ? AppColors.primary : AppColors.black.withValues(alpha: 0.6),
                              size: 20,
                            ),
                            SizedBox(width: 16),
                            Expanded(
                              child: Text(
                                category,
                                style: TextStyle(
                                  color: AppColors.black,
                                  fontSize: 16,
                                  fontFamily: 'Poppins',
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                ),
                              ),
                            ),
                            if (isSelected)
                              Icon(
                                Icons.check_circle,
                                color: AppColors.primary,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),

              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  // Helper method to get category icon
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Electronics':
        return Icons.devices;
      case 'Computer Accessories':
        return Icons.computer;
      case 'Clothing & Fashion':
        return Icons.checkroom;
      case 'Books & Documents':
        return Icons.menu_book;
      case 'Food & Beverages':
        return Icons.restaurant;
      case 'Home & Garden':
        return Icons.home;
      case 'Sports & Outdoors':
        return Icons.sports_soccer;
      case 'Health & Beauty':
        return Icons.spa;
      case 'Toys & Games':
        return Icons.toys;
      case 'Automotive':
        return Icons.directions_car;
      case 'Jewelry & Accessories':
        return Icons.diamond;
      case 'Art & Crafts':
        return Icons.palette;
      case 'Musical Instruments':
        return Icons.music_note;
      case 'Office Supplies':
        return Icons.business_center;
      case 'General':
      default:
        return Icons.category;
    }
  }

  // Helper method to get durability color (moved from add_cargo.dart)
  Color _getDurabilityColor(ItemDurability durability) {
    switch (durability) {
      case ItemDurability.fragile:
        return AppColors.error;
      case ItemDurability.average:
        return AppColors.warning;
      case ItemDurability.durable:
        return AppColors.success;
    }
  }

  // Helper method to get durability description
  String _getDurabilityDescription(ItemDurability durability) {
    switch (durability) {
      case ItemDurability.fragile:
        return 'Handle with extra care';
      case ItemDurability.average:
        return 'Standard handling required';
      case ItemDurability.durable:
        return 'Can withstand rough handling';
    }
  }

  Widget _buildImageSection(BuildContext context, List<String> selectedImages, Function(List<String>) onImagesChanged) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Add image button
          GestureDetector(
            onTap: () => _showImagePickerOptions(context, selectedImages, onImagesChanged),
            child: Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add_photo_alternate,
                    color: AppColors.primary,
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Add Images',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontSize: 14,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Selected images preview
          if (selectedImages.isNotEmpty) ...[
            SizedBox(height: 12),
            Container(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: EdgeInsets.only(right: 8),
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.black.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            File(selectedImages[index]),
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: AppColors.black.withValues(alpha: 0.1),
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: AppColors.black.withValues(alpha: 0.4),
                                  size: 24,
                                ),
                              );
                            },
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () {
                              final updatedImages = List<String>.from(selectedImages);
                              updatedImages.removeAt(index);
                              onImagesChanged(updatedImages);
                            },
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: AppColors.error,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.close,
                                color: AppColors.white,
                                size: 12,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showImagePickerOptions(BuildContext context, List<String> currentImages, Function(List<String>) onImagesChanged) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: EdgeInsets.all(20),
                child: Text(
                  'Add Image',
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: 18,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Options
              ListTile(
                leading: Icon(Icons.camera_alt, color: AppColors.primary),
                title: Text(
                  'Take Photo',
                  style: TextStyle(
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.camera, currentImages, onImagesChanged);
                },
              ),

              ListTile(
                leading: Icon(Icons.photo_library, color: AppColors.primary),
                title: Text(
                  'Choose from Gallery',
                  style: TextStyle(
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.gallery, currentImages, onImagesChanged);
                },
              ),

              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickImage(ImageSource source, List<String> currentImages, Function(List<String>) onImagesChanged) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        // Show preview dialog
        final confirmed = await ImagePreviewDialog.show(
          context,
          imagePath: image.path,
          title: 'Add this image?',
          confirmButtonText: 'Add Image',
          cancelButtonText: 'Cancel',
        );

        if (confirmed == true) {
          final updatedImages = List<String>.from(currentImages);
          updatedImages.add(image.path);
          onImagesChanged(updatedImages);
        }
      }
    } catch (e) {
      Toast.error('Failed to pick image: $e');
    }
  }

  void _handleDuplicateOrder() {
    // Create duplicates of all current cargo items
    if (_cargoItems.isNotEmpty) {
      final List<CargoItem> duplicatedItems = [];

      for (final item in _cargoItems) {
        final duplicatedItem = CargoItem(
          itemName: '${item.itemName} (Copy)',
          category: item.category,
          itemType: item.itemType,
          weight: item.weight,
          quantity: item.quantity,
          durability: item.durability,
          imagePaths: List<String>.from(item.imagePaths),
          canEdit: true,
          canDelete: true,
        );
        duplicatedItems.add(duplicatedItem);
      }

      setState(() {
        _cargoItems.addAll(duplicatedItems);
      });

      Toast.success('Order duplicated successfully (${duplicatedItems.length} items added)');
    } else {
      Toast.error('No items to duplicate');
    }
  }

  void _handleProceedToPayment() async {
    // Validate that we have cargo items
    if (_cargoItems.isEmpty) {
      Toast.error('Please add cargo items before proceeding to checkout');
      return;
    }

    setState(() {
      _isProcessingPayment = true;
    });

    try {
      // Create current order data
      final currentOrder = await _createOrderData();

      // Navigate to checkout page with current order using root navigator
      final result = await Navigator.of(context, rootNavigator: true).pushNamed(
        '/checkout',
        arguments: {
          'currentOrder': currentOrder,
          'onNavigateToHome': widget.onNavigateToHome,
        },
      );

      // Handle result from checkout page
      if (result == 'payment_success') {
        // Navigate back to home after successful payment
        if (widget.onNavigateToHome != null) {
          widget.onNavigateToHome!();
        } else {
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      }

    } catch (e) {
      Toast.error('Failed to proceed to checkout. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingPayment = false;
        });
      }
    }
  }

  /// Create order data for checkout
  Future<Map<String, dynamic>> _createOrderData() async {
    // Get pickup and receiver data
    final pickupData = await PackageDataService.getPickupData();
    final receiverData = await PackageDataService.getReceiverData();

    // Determine delivery type from package data
    String deliveryType = 'instant'; // Default to instant
    if (_packageData.isNotEmpty) {
      // Check if there's schedule data indicating scheduled delivery
      if (_packageData['scheduleData'] != null) {
        deliveryType = 'scheduled';
      } else if (_packageData['deliveryType'] != null) {
        deliveryType = _packageData['deliveryType'];
      }
    }

    // Create order data
    return {
      'pickupData': pickupData,
      'receiverData': receiverData,
      'packageData': {
        ..._packageData,
        'deliveryType': deliveryType, // Ensure delivery type is saved
      },
      'cargoItems': _cargoItems.map((item) => {
        'itemName': item.itemName,
        'category': item.category,
        'itemType': item.itemType,
        'weight': item.weight,
        'quantity': item.quantity,
        'durability': item.durability.displayName,
        'imagePaths': item.imagePaths,
      }).toList(),
      'paymentData': {
        'shippingCost': _paymentData.shippingCost,
        'vat': _paymentData.vat,
        'insurance': _paymentData.insurance,
        'pickupCharge': _paymentData.pickupCharge,
        'isInsuranceFree': _paymentData.isInsuranceFree,
        'isPickupFree': _paymentData.isPickupFree,
        'total': _paymentData.total,
      },
      'orderDate': DateTime.now().toIso8601String(),
      'trackingNumber': _generateTrackingNumber(),
      'status': 'pending', // Initial status for rider dashboard
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// Save order data to local storage (legacy method - kept for compatibility)
  Future<void> _saveOrderData() async {
    try {
      // Get pickup and receiver data
      final pickupData = await PackageDataService.getPickupData();
      final receiverData = await PackageDataService.getReceiverData();

      // Determine delivery type from package data
      String deliveryType = 'instant'; // Default to instant
      if (_packageData != null) {
        // Check if there's schedule data indicating scheduled delivery
        if (_packageData!['scheduleData'] != null) {
          deliveryType = 'scheduled';
        } else if (_packageData!['deliveryType'] != null) {
          deliveryType = _packageData!['deliveryType'];
        }
      }

      // Create order data
      final orderData = {
        'pickupData': pickupData,
        'receiverData': receiverData,
        'packageData': {
          ..._packageData ?? {},
          'deliveryType': deliveryType, // Ensure delivery type is saved
        },
        'cargoItems': _cargoItems.map((item) => {
          'itemName': item.itemName,
          'category': item.category,
          'itemType': item.itemType,
          'weight': item.weight,
          'quantity': item.quantity,
          'durability': item.durability.displayName,
          'imagePaths': item.imagePaths,
        }).toList(),
        'paymentData': {
          'shippingCost': _paymentData.shippingCost,
          'vat': _paymentData.vat,
          'insurance': _paymentData.insurance,
          'pickupCharge': _paymentData.pickupCharge,
          'isInsuranceFree': _paymentData.isInsuranceFree,
          'isPickupFree': _paymentData.isPickupFree,
          'total': _paymentData.total,
        },
        'orderDate': DateTime.now().toIso8601String(),
        'trackingNumber': _generateTrackingNumber(),
        'status': 'pending', // Initial status for rider dashboard
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Save as completed order
      await PackageDataService.saveCompletedOrder(orderData);

      // Also save as current shipment (for tracking)
      await PackageDataService.saveCurrentShipment(orderData);

    } catch (e) {
      print('Error saving order data: $e');
      // Don't throw error to prevent payment flow interruption
    }
  }

  /// Generate a tracking number
  String _generateTrackingNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 100000).toString().padLeft(5, '0');
    return '#RO$random';
  }

  void _handleSaveAsDraft() {
    // TODO: Implement save as draft functionality
    // This could save the current order state for later completion
    Toast.success('Order saved as draft');
  }

  void _handleViewAllOrders() {
    // Navigate to orders summary view using root navigator
    Navigator.of(context, rootNavigator: true).pushNamed('/orders-summary');
  }

  void _handleHelpTap() {
    // Navigate to customer support view
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CustomerSupportView(),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch - reduced spacing
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.0; // Tablet
    } else {
      spacing = baseSpacing; // Mobile - standard spacing
    }

    if (isShortScreen) {
      spacing = spacing * 0.8; // Reduce for short screens
    }

    return spacing;
  }

  double _getBorderRadius(BuildContext context, double baseRadius) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseRadius * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      return baseRadius * 1.2; // Tablet
    } else {
      return baseRadius; // Mobile
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6; // Smartwatch
    } else if (screenWidth > 600) {
      return 10; // Tablet
    } else {
      return 8; // Mobile
    }
  }

  double _getIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseSize * 0.8; // Smartwatch - smaller icons
    } else if (screenWidth > 600) {
      return baseSize * 1.2; // Tablet - larger icons
    } else {
      return baseSize; // Mobile - standard size
    }
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 16; // Smartwatch - smaller text
    } else if (screenWidth > 600) {
      baseSize = 24; // Tablet - larger text
    } else {
      baseSize = 20; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12; // Smartwatch - small button text
    } else if (screenWidth > 600) {
      baseSize = 18; // Tablet - larger button text
    } else {
      baseSize = 16; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40; // Smartwatch
    } else if (screenWidth > 600) {
      baseHeight = 56; // Tablet
    } else {
      baseHeight = 48; // Mobile
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9; // Reduce for short screens
    }

    return baseHeight;
  }
}