import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/services/package_data_service.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/address/address_request.dart';
import 'package:rideoon/models/address/address.dart';
import 'package:rideoon/models/api_response.dart';
import 'package:rideoon/providers/address_provider.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/location_confirmation_map.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Pickup Details screen for package delivery
///
/// This screen allows users to input pickup information including
/// sender name, destination state, full address, closest landmark, and phone number.
class PickupDetailsView extends StatefulWidget {
  const PickupDetailsView({super.key});

  @override
  State<PickupDetailsView> createState() => _PickupDetailsViewState();
}

class _PickupDetailsViewState extends State<PickupDetailsView> {
  final _formKey = GlobalKey<FormState>();
  final _senderNameController = TextEditingController();
  final _fullAddressController = TextEditingController();
  final _landmarkController = TextEditingController();
  final _phoneController = TextEditingController();

  String? _selectedPickupState;
  bool _isLoading = false;
  LatLng? _confirmedLocation;
  List<Map<String, dynamic>> _locationHistory = [];
  bool _hasUnsavedChanges = false;
  String? _originalAddress;
  String? _selectedAddressUuid; // Track UUID of selected address for editing



  // Nigerian states for dropdown
  final List<String> _nigerianStates = [
    'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue',
    'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu',
    'FCT - Abuja', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina',
    'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo',
    'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
  ];

  @override
  void initState() {
    super.initState();
    _originalAddress = ''; // Initialize to empty string
    _loadSavedData();
    _loadLocationHistory();
    _setupTextFieldListeners();
    _loadUserData(); // Auto-fill user data

    // Address provider will be accessed via Provider.of when needed
    // Load addresses initially
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AddressProvider>(context, listen: false).loadAddresses();
    });
  }



  @override
  void dispose() {
    _senderNameController.dispose();
    _fullAddressController.dispose();
    _landmarkController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  /// Load user data and auto-fill sender name and phone
  Future<void> _loadUserData() async {
    try {
      final account = await AuthService.getUserAccount();
      if (account != null) {
        setState(() {
          // Auto-fill sender name if not already filled
          if (_senderNameController.text.isEmpty) {
            _senderNameController.text = account.fullName;
          }

          // Auto-fill phone number if not already filled
          if (_phoneController.text.isEmpty) {
            // Remove country code if present and format for Nigerian numbers
            String phoneNumber = account.phoneNumber;
            if (phoneNumber.startsWith('+234')) {
              phoneNumber = phoneNumber.substring(4);
            } else if (phoneNumber.startsWith('234')) {
              phoneNumber = phoneNumber.substring(3);
            } else if (phoneNumber.startsWith('0')) {
              phoneNumber = phoneNumber.substring(1);
            }
            _phoneController.text = phoneNumber;
          }
        });
      }
    } catch (e) {
      // Silently handle errors - don't break the UI
      if (mounted) {
        print('Error loading user data: $e');
      }
    }
  }

  /// Load saved pickup data if available
  Future<void> _loadSavedData() async {
    final data = await PackageDataService.getPickupData();
    if (data != null) {
      setState(() {
        // Only fill if not already auto-filled from user data
        if (_senderNameController.text.isEmpty) {
          _senderNameController.text = data['senderName'] ?? '';
        }
        _fullAddressController.text = data['fullAddress'] ?? '';
        _landmarkController.text = data['landmark'] ?? '';
        if (_phoneController.text.isEmpty) {
          _phoneController.text = data['phone'] ?? '';
        }
        _selectedPickupState = data['state'];
        _originalAddress = data['fullAddress'] ?? '';

        // Load confirmed location if available
        if (data['latitude'] != null && data['longitude'] != null) {
          _confirmedLocation = LatLng(data['latitude'], data['longitude']);
        }
      });
    }
  }



  /// Setup text field listeners to detect changes
  void _setupTextFieldListeners() {
    // Listen to address changes
    _fullAddressController.addListener(() {
      final currentAddress = _fullAddressController.text.trim();
      if (currentAddress != _originalAddress) {
        setState(() {
          _hasUnsavedChanges = true;
          _confirmedLocation = null; // Reset location confirmation
        });
      }
    });

    // Listen to other field changes to detect unsaved changes
    _senderNameController.addListener(() {
      setState(() {
        _hasUnsavedChanges = true;
      });
    });

    _landmarkController.addListener(() {
      setState(() {
        _hasUnsavedChanges = true;
      });
    });

    _phoneController.addListener(() {
      setState(() {
        _hasUnsavedChanges = true;
      });
    });
  }

  /// Load location history
  Future<void> _loadLocationHistory() async {
    final history = await PackageDataService.getPickupLocationHistory();
    setState(() {
      _locationHistory = history;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: _getSpacing(context, 33),
                vertical: _getSpacing(context, 16),
              ),
              child: _buildHeader(context),
            ),

            // Divider
            _buildDivider(context),

            // Form content with keyboard-aware layout
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: _getSpacing(context, 33)),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: _getSpacing(context, 32)),

                      // Sender Name
                      _buildLabeledField(
                        label: 'Sender Name',
                        child: _buildTextFormField(
                          controller: _senderNameController,
                          hintText: 'Enter sender name',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter sender name';
                            }
                            return null;
                          },
                        ),
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Pickup State
                      _buildLabeledField(
                        label: 'Pickup State',
                        child: _buildStateSelector(context),
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Full Address
                      _buildLabeledField(
                        label: 'Full Address',
                        child: _buildTextFormField(
                          controller: _fullAddressController,
                          hintText: 'Enter full address',
                          maxLines: 2,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter full address';
                            }
                            return null;
                          },
                        ),
                      ),

                      SizedBox(height: _getSpacing(context, 12)),

                      // Confirm Location Button
                      _buildConfirmLocationButton(context),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Saved Addresses (if available)
                      Consumer<AddressProvider>(
                        builder: (context, addressProvider, child) {
                          final savedAddresses = addressProvider.pickupAddresses;
                          if (savedAddresses.isNotEmpty) {
                            return Column(
                              children: [
                                _buildSavedAddresses(context, savedAddresses),
                                SizedBox(height: _getSpacing(context, 20)),
                              ],
                            );
                          }
                          return SizedBox.shrink();
                        },
                      ),

                      // Location History (if available)
                      if (_locationHistory.isNotEmpty) ...[
                        _buildLocationHistory(context),
                        SizedBox(height: _getSpacing(context, 20)),
                      ],

                      // Closest Landmark
                      _buildLabeledField(
                        label: 'Closest Landmark',
                        child: _buildTextFormField(
                          controller: _landmarkController,
                          hintText: 'Enter closest landmark',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter closest landmark';
                            }
                            return null;
                          },
                        ),
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Phone Number
                      _buildLabeledField(
                        label: 'Phone Number',
                        child: _buildPhoneNumberField(context),
                      ),

                      SizedBox(height: _getSpacing(context, 32)),

                      // Continue Button (now inside scrollable area)
                      _buildContinueButton(context),

                      // Bottom padding to ensure button is visible above keyboard
                      SizedBox(height: MediaQuery.of(context).viewInsets.bottom + _getSpacing(context, 24)),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Container(
            width: _getIconSize(context) + 8,
            height: _getIconSize(context) + 8,
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(_getBorderRadius(context)),
            ),
            child: Icon(
              Icons.arrow_back_ios_new,
              size: _getIconSize(context),
              color: AppColors.black,
            ),
          ),
        ),
        Expanded(
          child: Center(
            child: Text(
              'Pickup Details',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getHeadingFontSize(context),
                fontFamily: 'Bricolage Grotesque',
                fontWeight: FontWeight.w600,
                letterSpacing: -0.8,
              ),
            ),
          ),
        ),
        SizedBox(width: _getIconSize(context) + 8), // Balance the back button
      ],
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getSpacing(context, 33)),
      child: Container(
        width: double.infinity,
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              strokeAlign: BorderSide.strokeAlignCenter,
              color: AppColors.black.withValues(alpha: 0.1),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLabeledField({
    required String label,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppColors.black,
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String hintText,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0x0A1E1E1E),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        validator: validator,
        style: TextStyle(
          color: Colors.black.withValues(alpha: 0.88),
          fontSize: 14,
          fontFamily: 'Inter',
          fontWeight: FontWeight.w400,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: Colors.black.withValues(alpha: 0.37),
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
        ),
      ),
    );
  }

  Widget _buildStateSelector(BuildContext context) {
    return GestureDetector(
      onTap: () => _showStateBottomSheet(context),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0x0A1E1E1E),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.02),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _selectedPickupState ?? 'Select pickup state',
                style: TextStyle(
                  color: _selectedPickupState != null
                      ? Colors.black.withValues(alpha: 0.88)
                      : Colors.black.withValues(alpha: 0.37),
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: Colors.black.withValues(alpha: 0.37),
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneNumberField(BuildContext context) {
    return Row(
      children: [
        // Country code container
        Container(
          width: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0x0A1E1E1E),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.02),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            child: Text(
              '+234',
              style: TextStyle(
                color: Colors.black.withValues(alpha: 0.88),
                fontSize: 14,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),

        const SizedBox(width: 12),

        // Phone number field
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0x0A1E1E1E),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.02),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter phone number';
                }
                if (value.length < 10) {
                  return 'Please enter a valid phone number';
                }
                return null;
              },
              style: TextStyle(
                color: Colors.black.withValues(alpha: 0.88),
                fontSize: 14,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
              decoration: InputDecoration(
                hintText: 'Enter phone number',
                hintStyle: TextStyle(
                  color: Colors.black.withValues(alpha: 0.37),
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleContinue,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'Continue',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildConfirmLocationButton(BuildContext context) {
    final hasAddress = _fullAddressController.text.trim().isNotEmpty;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: hasAddress && !_isLoading ? _confirmLocationOnMap : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: _confirmedLocation != null
              ? AppColors.success
              : AppColors.primary.withValues(alpha: hasAddress ? 1.0 : 0.5),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        icon: Icon(
          _confirmedLocation != null ? Icons.check_circle : Icons.location_on,
          size: 20,
        ),
        label: Text(
          _confirmedLocation != null && !_hasUnsavedChanges
              ? 'Location Confirmed'
              : 'Confirm location on map',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildSavedAddresses(BuildContext context, List<Map<String, dynamic>> savedAddresses) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Saved Pickup Addresses',
          style: TextStyle(
            color: AppColors.black,
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          constraints: BoxConstraints(maxHeight: 150),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: savedAddresses.length > 3 ? 3 : savedAddresses.length,
            itemBuilder: (context, index) {
              final address = savedAddresses[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListTile(
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  leading: Icon(
                    Icons.bookmark,
                    size: 20,
                    color: AppColors.primary,
                  ),
                  title: Text(
                    address['name'] ?? address['addressName'] ?? 'Saved Address',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      color: AppColors.black.withValues(alpha: 0.9),
                    ),
                  ),
                  subtitle: Text(
                    address['street'] ?? address['fullAddress'] ?? '',
                    style: TextStyle(
                      fontSize: 11,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      color: AppColors.black.withValues(alpha: 0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    setState(() {
                      // Use the correct field names from API response
                      _senderNameController.text = address['name'] ?? address['addressName'] ?? '';
                      _fullAddressController.text = address['street'] ?? address['fullAddress'] ?? '';
                      _landmarkController.text = ''; // Landmark is part of street in API
                      _phoneController.text = address['phoneNumber']?.toString() ?? address['phone'] ?? '';
                      _selectedPickupState = address['state'] ?? address['city'];

                      // Parse coordinates
                      if (address['latitude'] != null && address['longitude'] != null) {
                        final lat = double.tryParse(address['latitude'].toString());
                        final lng = double.tryParse(address['longitude'].toString());
                        if (lat != null && lng != null) {
                          _confirmedLocation = LatLng(lat, lng);
                        }
                      }

                      _originalAddress = address['street'] ?? address['fullAddress'] ?? '';
                      _selectedAddressUuid = address['id']; // Store UUID for editing
                      _hasUnsavedChanges = false;
                    });

                    // Mark as recently used (legacy method - no-op in API version)
                    AddressService.markAsRecentlyUsed(address['id'] ?? '');
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLocationHistory(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Locations',
          style: TextStyle(
            color: AppColors.black,
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          constraints: BoxConstraints(maxHeight: 150),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _locationHistory.length > 3 ? 3 : _locationHistory.length,
            itemBuilder: (context, index) {
              final location = _locationHistory[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListTile(
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  leading: Icon(
                    Icons.history,
                    size: 20,
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                  title: Text(
                    location['address'] ?? '',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      color: AppColors.black.withValues(alpha: 0.8),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    setState(() {
                      _fullAddressController.text = location['address'] ?? '';
                      if (location['latitude'] != null && location['longitude'] != null) {
                        _confirmedLocation = LatLng(location['latitude'], location['longitude']);
                        // If we have coordinates from history, consider it confirmed
                        _hasUnsavedChanges = false;
                        _originalAddress = location['address'] ?? '';
                      } else {
                        // If no coordinates, user needs to confirm location
                        _hasUnsavedChanges = true;
                      }
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<void> _confirmLocationOnMap() async {
    final address = _fullAddressController.text.trim();
    if (address.isEmpty || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await Navigator.of(context).push<Map<String, dynamic>>(
        MaterialPageRoute(
          builder: (context) => LocationConfirmationMap(
            address: address,
            title: 'Pickup Location',
            initialCoordinates: _confirmedLocation,
          ),
        ),
      );

      if (result != null && mounted) {
        setState(() {
          _confirmedLocation = result['coordinates'];
          _fullAddressController.text = result['address'];
          _originalAddress = result['address'];
          _hasUnsavedChanges = false; // Reset since location is now confirmed
        });

        // Save to location history
        await PackageDataService.savePickupLocationToHistory({
          'address': result['address'],
          'latitude': result['latitude'],
          'longitude': result['longitude'],
        });

        // Reload location history
        await _loadLocationHistory();
      }
    } catch (e) {
      // Handle any errors gracefully
      if (mounted) {
        Toast.error('Failed to confirm location: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showStateBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                'Select Pickup State',
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: 18,
                  fontFamily: 'Bricolage Grotesque',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            // States list
            Expanded(
              child: ListView.builder(
                itemCount: _nigerianStates.length,
                itemBuilder: (context, index) {
                  final state = _nigerianStates[index];
                  return ListTile(
                    title: Text(
                      state,
                      style: TextStyle(
                        color: AppColors.black,
                        fontSize: 16,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    onTap: () {
                      setState(() {
                        _selectedPickupState = state;
                      });
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleContinue() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedPickupState == null) {
        Toast.error('Please select a pickup state');
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // Save pickup data to local storage
        final pickupData = {
          'senderName': _senderNameController.text.trim(),
          'fullAddress': _fullAddressController.text.trim(),
          'landmark': _landmarkController.text.trim(),
          'phone': _phoneController.text.trim(),
          'state': _selectedPickupState,
          'latitude': _confirmedLocation?.latitude,
          'longitude': _confirmedLocation?.longitude,
        };

        await PackageDataService.savePickupData(pickupData);

        // Show save address dialog if this is a new/modified address
        final currentAddress = _fullAddressController.text.trim();
        final shouldShowSaveDialog = _hasUnsavedChanges ||
                                   _originalAddress != currentAddress ||
                                   _originalAddress == null ||
                                   currentAddress.isNotEmpty;

        if (shouldShowSaveDialog && currentAddress.isNotEmpty) {
          await _showSaveAddressDialog(pickupData);
        }

        // Show success toast
        Toast.success('Pickup details saved successfully');

        // Navigate back to send package view
        if (mounted) {
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      } catch (e) {
        Toast.error('Failed to save pickup details: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show dialog to ask user if they want to save the address
  Future<void> _showSaveAddressDialog(Map<String, dynamic> pickupData) async {
    await PushNotificationDialog.show(
      context,
      title: 'Save Address',
      description: 'Do you want to save this address for later use?',
      acceptButtonText: 'Yes, Save',
      declineButtonText: 'No, Thanks',
      icon: Icons.bookmark_add,
      iconBackgroundColor: AppColors.primary,
      iconColor: AppColors.primary,
      onAccept: () async {
        try {
          // Create AddressRequest from pickup data
          final addressRequest = AddressRequest(
            name: pickupData['senderName'] ?? 'Unknown',
            phoneNumber: int.tryParse(pickupData['phone']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0,
            street: '${pickupData['fullAddress']} ${pickupData['landmark'] ?? ''}'.trim(),
            city: pickupData['state'] ?? '',
            state: pickupData['state'] ?? '',
            country: 'Nigeria',
            type: 'pickup',
            longitude: pickupData['longitude'] != null ? double.tryParse(pickupData['longitude'].toString()) : null,
            latitude: pickupData['latitude'] != null ? double.tryParse(pickupData['latitude'].toString()) : null,
          );

          // Get auth token from stored authentication data
          final authToken = await AuthService.getAuthToken();

          if (authToken != null) {
            // Check if we're editing an existing address or creating a new one
            late final ApiResponse<Address> response;
            if (_selectedAddressUuid != null && _selectedAddressUuid!.isNotEmpty) {
              // Update existing address
              response = await AddressService.updateAddress(_selectedAddressUuid!, addressRequest, authToken: authToken);
            } else {
              // Create new address
              response = await AddressService.createAddress(addressRequest, authToken: authToken);
            }

            if (response.success) {
              // Refresh saved addresses using Provider
              if (mounted) {
                Provider.of<AddressProvider>(context, listen: false).refresh();
              }
              final action = _selectedAddressUuid != null ? 'updated' : 'saved';
              Toast.success('Address $action successfully');
            } else {
              Toast.error('Failed to save address: ${response.message}');
            }
          } else {
            Toast.error('Authentication required to save address');
          }
        } catch (e) {
          Toast.error('Failed to save address: $e');
        }
      },
      onDecline: () {
        // Dialog will close automatically
      },
    );
  }

  // Helper methods for responsive design
  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 350) return baseSpacing * 0.8;
    if (screenWidth > 600) return baseSpacing * 1.2;
    return baseSpacing;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 350) return 18;
    if (screenWidth > 600) return 24;
    return 20;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 350) return 6;
    if (screenWidth > 600) return 10;
    return 8;
  }

  double _getHeadingFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 350) return 18;
    if (screenWidth > 600) return 24;
    return 20;
  }
}