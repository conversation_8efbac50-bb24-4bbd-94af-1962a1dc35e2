import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/views/custom_widgets/payment_summary.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/instant_delivery.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/scheduled_delivery.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/pickup_details.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/reciever_details.dart';
import 'package:rideoon/views/dashboards/user_dashboard/checkout_and_cargo_view/checkout_and_cargo_view.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/services/package_data_service.dart';
import 'package:rideoon/services/order_service.dart';
import 'package:rideoon/views/custom_widgets/floating_orders_button.dart';

/// Send Package view page for user dashboard
///
/// This page displays a tabbed interface for instant and scheduled delivery options.
/// Users can fill out package details and add them to cargo.
class SendPackageView extends StatefulWidget {
  /// Callback function to navigate back to home tab
  final VoidCallback? onNavigateToHome;

  const SendPackageView({super.key, this.onNavigateToHome});

  @override
  State<SendPackageView> createState() => _SendPackageViewState();
}

class _SendPackageViewState extends State<SendPackageView> {
  int _selectedTabIndex = 0;

  // Cargo management
  List<CargoItem> _cargoItems = [];

  // Package data from forms
  Map<String, dynamic> _packageData = {
    'deliveryType': 'instant', // 'instant' or 'scheduled'
    'pickupDetails': {},
    'receiverDetails': {},
    'packageDetails': {},
  };

  // Track status of pickup and receiver details
  bool _hasPickupDetails = false;
  bool _hasReceiverDetails = false;

  // Order count for floating button badge
  int _orderCount = 0;

  @override
  void initState() {
    super.initState();
    _checkExistingDetails();
    _loadOrderCount();
  }

  /// Check if pickup and receiver details already exist
  Future<void> _checkExistingDetails() async {
    final hasPickup = await PackageDataService.hasPickupData();
    final hasReceiver = await PackageDataService.hasReceiverData();

    setState(() {
      _hasPickupDetails = hasPickup;
      _hasReceiverDetails = hasReceiver;
    });
  }

  /// Load order count for floating button badge
  Future<void> _loadOrderCount() async {
    try {
      // Get counts from both services to ensure accuracy
      final packageOrders = await PackageDataService.getCompletedOrders();
      final orderServiceCompleted = await OrderService.getCompletedOrders();
      final orderServiceDrafts = await OrderService.getDraftOrders();

      // Calculate total count from all sources
      final totalCount = packageOrders.length + orderServiceCompleted.length + orderServiceDrafts.length;

      if (mounted) {
        setState(() {
          _orderCount = totalCount;
        });
      }
    } catch (e) {
      print('Error loading order count: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        children: [
          // Main content
          Column(
            children: [
              // Header with back button, title (moved to top)
              Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + 10,
                  bottom: 10,
                ),
                child: _buildHeader(context),
              ),

              // Main content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: _getSpacing(context, 19)),

                    // Divider
                    _buildDivider(context),

                    SizedBox(height: _getSpacing(context, 24)),

                    // Tab buttons
                    _buildTabButtons(context),

                    SizedBox(height: _getSpacing(context, 24)),

                    // Tab content
                    Expanded(
                      child: Column(
                        children: [
                          Expanded(
                            child: _buildTabContent(context),
                          ),

                          // Continue Button (moved to bottom)
                          _buildContinueButton(context),

                          SizedBox(height: _getSpacing(context, 20)), // Bottom padding
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Floating Orders Button (30% higher)
          FloatingOrdersButton(
            onTap: _handleViewAllOrders,
            orderCount: _orderCount,
            bottom: MediaQuery.of(context).size.height * 0.13, // 30% higher than default
          ),
        ],
      ),
    );
  }

  Widget _buildTabButtons(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Row(
        children: [
          // Instant Delivery tab
          Expanded(
            child: _buildTabButton(
              context,
              title: 'Instant Delivery',
              isSelected: _selectedTabIndex == 0,
              onTap: () => setState(() => _selectedTabIndex = 0),
            ),
          ),

          SizedBox(width: _getSpacing(context, 8)),

          // Scheduled Delivery tab
          Expanded(
            child: _buildTabButton(
              context,
              title: 'Scheduled Delivery',
              isSelected: _selectedTabIndex == 1,
              onTap: () => setState(() => _selectedTabIndex = 1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton(
    BuildContext context, {
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 18)), // Increased padding
        decoration: ShapeDecoration(
          color: isSelected ? AppColors.primary : AppColors.white,
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1.5, // Thicker border
              color: isSelected
                  ? AppColors.primary
                  : const Color(0x1A1E1E1E), // More visible border
            ),
            borderRadius: BorderRadius.circular(20), // Increased border radius
          ),
          shadows: isSelected ? [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? AppColors.white : const Color(0xFF111111),
              fontSize: 14, // Increased font size
              fontFamily: 'Inter',
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500, // Bolder weight
              height: 1.4,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent(BuildContext context) {
    // Add a unique key to force widget rebuild when status changes
    final key = ValueKey('${_selectedTabIndex}_${_hasPickupDetails}_${_hasReceiverDetails}');

    switch (_selectedTabIndex) {
      case 0:
        return InstantDeliveryWidget(
          key: key,
          onAddToCargo: _handleAddToCargo,
          onAddPickupDetails: _handleAddPickupDetails,
          onAddReceiverDetails: _handleAddReceiverDetails,
          onDataChanged: (data) => _updatePackageData('instant', data),
          hasPickupDetails: _hasPickupDetails,
          hasReceiverDetails: _hasReceiverDetails,
        );
      case 1:
        return ScheduledDeliveryWidget(
          key: key,
          onAddToCargo: _handleAddToCargo,
          onAddPickupDetails: _handleAddPickupDetails,
          onAddReceiverDetails: _handleAddReceiverDetails,
          onDataChanged: (data) => _updatePackageData('scheduled', data),
          hasPickupDetails: _hasPickupDetails,
          hasReceiverDetails: _hasReceiverDetails,
        );
      default:
        return InstantDeliveryWidget(
          key: key,
          onAddToCargo: _handleAddToCargo,
          onAddPickupDetails: _handleAddPickupDetails,
          onAddReceiverDetails: _handleAddReceiverDetails,
          onDataChanged: (data) => _updatePackageData('instant', data),
          hasPickupDetails: _hasPickupDetails,
          hasReceiverDetails: _hasReceiverDetails,
        );
    }
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getResponsiveSpacing(context, 33)),
      child: Row(
        children: [
          // Back button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(26.5),
              onTap: () {
                // Check if we can pop, otherwise navigate to home tab
                if (Navigator.of(context).canPop()) {
                  Navigator.of(context).pop();
                } else {
                  // Navigate back to home tab (index 0)
                  _navigateToHome(context);
                }
              },
              child: Container(
                width: _getResponsiveSpacing(context, 53),
                height: _getResponsiveSpacing(context, 53),
                decoration: ShapeDecoration(
                  color: AppColors.white,
                  shape: const OvalBorder(),
                  shadows: [
                    BoxShadow(
                      color: AppColors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.arrow_back,
                  color: AppColors.black,
                  size: _getResponsiveSpacing(context, 24),
                ),
              ),
            ),
          ),
          SizedBox(width: _getResponsiveSpacing(context, 32)),

          // Title
          Expanded(
            child: Text(
              'Send a Package',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getResponsiveFontSize(context, 24), // Increased font size
                fontFamily: 'Bricolage Grotesque',
                fontWeight: FontWeight.w700, // Bolder weight
                letterSpacing: -0.60, // Adjusted letter spacing
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getResponsiveSpacing(context, 33)),
      child: Container(
        width: _getResponsiveSpacing(context, 324),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              strokeAlign: BorderSide.strokeAlignCenter,
              color: AppColors.black.withValues(alpha: 0.1),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      width: double.infinity,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _handleContinue,
          borderRadius: BorderRadius.circular(22), // Increased border radius
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20), // Increased padding
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(22), // Increased border radius
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.4), // More prominent shadow
                  blurRadius: 16, // Increased blur
                  offset: const Offset(0, 6), // Increased offset
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Text(
                'Continue',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16, // Increased font size
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w700, // Bolder weight
                  height: 1.3,
                  letterSpacing: 0.5, // Added letter spacing
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }



  // Action handlers
  void _handleContinue() async {
    // Check if required details are provided
    if (!_hasPickupDetails || !_hasReceiverDetails) {
      String missingDetails = '';
      if (!_hasPickupDetails && !_hasReceiverDetails) {
        missingDetails = 'pickup and receiver details';
      } else if (!_hasPickupDetails) {
        missingDetails = 'pickup details';
      } else {
        missingDetails = 'receiver details';
      }

      Toast.error('Please add $missingDetails before continuing');
      return;
    }

    // Fetch pickup and receiver details from PackageDataService
    final pickupDetails = await PackageDataService.getPickupData();
    final receiverDetails = await PackageDataService.getReceiverData();

    // Update package data with fetched details
    _packageData['pickupDetails'] = pickupDetails ?? {};
    _packageData['receiverDetails'] = receiverDetails ?? {};

    // Show success toast for delivery details
    Toast.success('Delivery details successfully added');

    // Navigate to checkout and cargo view with collected data
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CheckoutAndCargoView(
          initialPaymentData: _generatePaymentData(),
          initialCargoItems: _cargoItems,
          packageData: _packageData,
          onNavigateBack: () {
            Navigator.of(context).pop();
          },
          onNavigateToHome: () {
            // Navigate back to home after successful checkout
            _navigateToHome(context);
          },
          onNavigateToSendPackage: () {
            // Navigate back to send package view without clearing data
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  void _handleAddToCargo() {
    // Add current package data to cargo
    final cargoItem = _createCargoItemFromPackageData();
    if (cargoItem != null) {
      setState(() {
        _cargoItems.add(cargoItem);
      });

      // Show success toast
      Toast.success('Package added to cargo successfully');
    } else {
      Toast.error('Please fill in package details first');
    }
  }

  void _handleAddPickupDetails() async {
    // Navigate to pickup details screen and await result
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const PickupDetailsView(),
      ),
    );

    // Always refresh state when returning, regardless of result
    // This ensures the UI updates properly
    await _checkExistingDetails();

    // Force a rebuild to update the delivery widgets
    setState(() {});
  }

  void _handleAddReceiverDetails() async {
    // Navigate to receiver details screen and await result
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const ReceiverDetailsView(),
      ),
    );

    // Always refresh state when returning, regardless of result
    // This ensures the UI updates properly
    await _checkExistingDetails();

    // Force a rebuild to update the delivery widgets
    setState(() {});
  }

  void _handleViewCargo() {
    if (_cargoItems.isEmpty) {
      // Show empty cargo message with toast
      Toast.warning('No items in cargo yet');
      return;
    }

    // Show cargo dialog with first 3 items
    _showCargoDialog();
  }

  void _showCargoDialog() {
    final displayItems = _cargoItems.take(3).toList();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: Offset(0, 8),
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Cargo Items (${_cargoItems.length})',
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 18,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        color: AppColors.white,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Cargo items
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16),
                  child: AddCargo(
                    cargoItems: displayItems,
                    showActionButtons: false,
                    showShadow: false,
                    horizontalPadding: 0,
                  ),
                ),
              ),

              // Action buttons
              Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    if (_cargoItems.length > 3)
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _handleContinue(); // Go to full checkout view
                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.primary,
                            side: BorderSide(color: AppColors.primary),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'View All (${_cargoItems.length})',
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    if (_cargoItems.length > 3) SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _handleContinue(); // Go to checkout
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Proceed to Checkout',
                          style: TextStyle(
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToHome(BuildContext context) {
    // Use callback to switch to home tab if available
    if (widget.onNavigateToHome != null) {
      widget.onNavigateToHome!();
    } else {
      // Fallback: Try to find the parent dashboard and switch to home tab
      try {
        // Try to pop until we reach the dashboard
        Navigator.of(context).popUntil((route) => route.isFirst);
      } catch (e) {
        // If that fails, try to navigate to root
        Navigator.of(context).pushReplacementNamed('/');
      }
    }
  }

  void _handleViewAllOrders() {
    // Navigate to orders summary view
    Navigator.of(context).pushNamed('/orders-summary');
  }

  // Data management methods
  void _updatePackageData(String deliveryType, Map<String, dynamic> data) {
    setState(() {
      _packageData['deliveryType'] = deliveryType;
      _packageData['packageDetails'] = data;
    });
  }

  PaymentSummaryData _generatePaymentData() {
    // Calculate shipping cost based on package data
    double baseShippingCost = 2600; // Base cost
    double additionalCost = 0;

    // Add cost based on delivery type
    if (_packageData['deliveryType'] == 'scheduled') {
      additionalCost += 500; // Scheduled delivery premium
    }

    // Add cost based on number of cargo items (only if there are additional items beyond the first)
    if (_cargoItems.length > 1) {
      additionalCost += (_cargoItems.length - 1) * 300; // Additional items cost
    }

    return PaymentSummaryData(
      shippingCost: baseShippingCost + additionalCost,
      vat: (baseShippingCost + additionalCost) * 0.075, // 7.5% VAT
      insurance: 0,
      pickupCharge: 0,
      isInsuranceFree: true,
      isPickupFree: true,
    );
  }



  CargoItem? _createCargoItemFromPackageData() {
    final packageDetails = _packageData['packageDetails'] as Map<String, dynamic>?;

    if (packageDetails == null || packageDetails.isEmpty) {
      return null;
    }

    return CargoItem(
      itemName: packageDetails['itemName'] ?? 'Package Item',
      category: packageDetails['category'] ?? 'General',
      itemType: packageDetails['itemType'] ?? 'Package',
      weight: packageDetails['weight']?.toDouble() ?? 1.0,
      quantity: packageDetails['quantity']?.toInt() ?? 1,
      durability: _parseDurability(packageDetails['durability']) ?? ItemDurability.average,
      imagePaths: packageDetails['imagePaths'] != null
          ? List<String>.from(packageDetails['imagePaths'])
          : [],
      canEdit: true,
      canDelete: true,
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16;
    if (screenWidth > 600) return 40;
    return 24;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.7;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.3;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.85;
    }

    return spacing;
  }

  double _getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  ItemDurability? _parseDurability(dynamic durabilityValue) {
    if (durabilityValue == null) return null;

    final durabilityString = durabilityValue.toString().toLowerCase();
    switch (durabilityString) {
      case 'fragile':
        return ItemDurability.fragile;
      case 'average':
        return ItemDurability.average;
      case 'durable':
        return ItemDurability.durable;
      default:
        return null;
    }
  }
}
