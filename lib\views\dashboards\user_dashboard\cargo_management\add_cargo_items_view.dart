import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Add Cargo Items view page for managing multiple cargo items
///
/// This page allows users to add multiple cargo items to their order.
/// Users can add, edit, and delete cargo items before returning to the
/// checkout and cargo view.
///
/// Features:
/// - Add multiple cargo items
/// - Edit existing cargo items
/// - Delete cargo items
/// - Responsive design for mobile, tablet, and smartwatch
/// - Consistent styling with app theme
class AddCargoItemsView extends StatefulWidget {
  const AddCargoItemsView({super.key});

  @override
  State<AddCargoItemsView> createState() => _AddCargoItemsViewState();
}

class _AddCargoItemsViewState extends State<AddCargoItemsView> {
  List<CargoItem> _cargoItems = [];
  Map<String, dynamic> _packageData = {};

  // Available categories for cargo items
  static const List<String> _availableCategories = [
    'Electronics',
    'Computer Accessories',
    'Clothing & Fashion',
    'Books & Documents',
    'Food & Beverages',
    'Home & Garden',
    'Sports & Outdoors',
    'Health & Beauty',
    'Toys & Games',
    'Automotive',
    'Jewelry & Accessories',
    'Art & Crafts',
    'Musical Instruments',
    'Office Supplies',
    'General',
  ];

  @override
  void initState() {
    super.initState();
    // Get arguments passed from previous screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (args != null) {
        setState(() {
          _cargoItems = List<CargoItem>.from(args['existingItems'] ?? []);
          _packageData = args['packageData'] ?? {};
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(context),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(_getHorizontalPadding(context)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: _getSpacing(context, 24)),

                    // Instructions
                    _buildInstructions(context),

                    SizedBox(height: _getSpacing(context, 24)),

                    // Add Item Button
                    _buildAddItemButton(context),

                    SizedBox(height: _getSpacing(context, 24)),

                    // Cargo Items List
                    if (_cargoItems.isNotEmpty) ...[
                      _buildCargoItemsList(context),
                    ] else ...[
                      _buildEmptyState(context),
                    ],

                    SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom buttons
                  ],
                ),
              ),
            ),

            // Bottom Action Buttons
            _buildBottomActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: _getIconSize(context, 40),
              height: _getIconSize(context, 40),
              decoration: BoxDecoration(
                color: AppColors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                size: _getIconSize(context, 20),
                color: AppColors.black,
              ),
            ),
          ),
          SizedBox(width: _getSpacing(context, 16)),

          // Title
          Expanded(
            child: Text(
              'Add Cargo Items',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getTitleFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
                letterSpacing: -0.5,
              ),
            ),
          ),

          // Items count badge
          if (_cargoItems.isNotEmpty)
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: _getSpacing(context, 12),
                vertical: _getSpacing(context, 6),
              ),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
              ),
              child: Text(
                '${_cargoItems.length}',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: _getBodyFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInstructions(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppColors.primary,
            size: _getIconSize(context, 24),
          ),
          SizedBox(width: _getSpacing(context, 12)),
          Expanded(
            child: Text(
              'Add all the items you want to include in this order. You can add multiple items and edit them as needed.',
              style: TextStyle(
                color: AppColors.black.withValues(alpha: 0.8),
                fontSize: _getBodyFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddItemButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: _getButtonHeight(context),
      child: ElevatedButton.icon(
        onPressed: _handleAddItem,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
          ),
          elevation: 2,
        ),
        icon: Icon(
          Icons.add,
          size: _getIconSize(context, 20),
        ),
        label: Text(
          'Add New Item',
          style: TextStyle(
            fontSize: _getButtonFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildCargoItemsList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cargo Items (${_cargoItems.length})',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getTitleFontSize(context) - 2,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        AddCargo(
          cargoItems: _cargoItems,
          onEditItem: _handleEditItem,
          onDeleteItem: _handleDeleteItem,
          showShadow: true,
          showActionButtons: false, // Hide action buttons in this view
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 40)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 18)),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: _getIconSize(context, 64),
            color: AppColors.black.withValues(alpha: 0.4),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            'No cargo items added yet',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.6),
              fontSize: _getBodyFontSize(context) + 2,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            'Tap "Add New Item" to start adding cargo items to your order',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.5),
              fontSize: _getBodyFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Cancel button
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                backgroundColor: AppColors.white,
                foregroundColor: AppColors.black.withValues(alpha: 0.7),
                side: BorderSide(
                  width: 1.5,
                  color: AppColors.black.withValues(alpha: 0.2),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                minimumSize: Size(0, _getButtonHeight(context)),
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontSize: _getButtonFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

          SizedBox(width: _getSpacing(context, 12)),

          // Save button
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _cargoItems.isNotEmpty ? _handleSaveItems : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                minimumSize: Size(0, _getButtonHeight(context)),
                elevation: 0,
              ),
              child: Text(
                _cargoItems.isEmpty 
                    ? 'Add Items First' 
                    : 'Save Items (${_cargoItems.length})',
                style: TextStyle(
                  fontSize: _getButtonFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _handleAddItem() async {
    final result = await Navigator.of(context).pushNamed(
      '/add-single-cargo-item',
      arguments: {
        'existingItem': null,
        'itemIndex': -1,
      },
    );

    if (result != null && result is Map<String, dynamic>) {
      final item = result['item'] as CargoItem;
      setState(() {
        _cargoItems.add(item);
      });
      Toast.success('Cargo item added');
    }
  }

  void _handleEditItem(int index) async {
    if (index < _cargoItems.length) {
      final result = await Navigator.of(context).pushNamed(
        '/add-single-cargo-item',
        arguments: {
          'existingItem': _cargoItems[index],
          'itemIndex': index,
        },
      );

      if (result != null && result is Map<String, dynamic>) {
        final item = result['item'] as CargoItem;
        final itemIndex = result['index'] as int;
        final isEdit = result['isEdit'] as bool;

        if (isEdit && itemIndex >= 0 && itemIndex < _cargoItems.length) {
          setState(() {
            _cargoItems[itemIndex] = item;
          });
          Toast.success('Cargo item updated');
        }
      }
    }
  }

  void _handleDeleteItem(int index) {
    if (index < _cargoItems.length) {
      setState(() {
        _cargoItems.removeAt(index);
      });
      Toast.success('Cargo item removed');
    }
  }

  void _handleSaveItems() {
    // Return the cargo items to the previous screen
    Navigator.of(context).pop(_cargoItems);
  }





  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16;
    if (screenWidth > 600) return 40;
    return 24;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getBorderRadius(BuildContext context, double baseRadius) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseRadius * 0.8;
    } else if (screenWidth > 600) {
      return baseRadius * 1.2;
    } else {
      return baseRadius;
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6;
    } else if (screenWidth > 600) {
      return 10;
    } else {
      return 8;
    }
  }

  double _getIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseSize * 0.8;
    } else if (screenWidth > 600) {
      return baseSize * 1.2;
    } else {
      return baseSize;
    }
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 16;
    } else if (screenWidth > 600) {
      baseSize = 24;
    } else {
      baseSize = 20;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10;
    } else if (screenWidth > 600) {
      baseSize = 16;
    } else {
      baseSize = 14;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12;
    } else if (screenWidth > 600) {
      baseSize = 18;
    } else {
      baseSize = 16;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40;
    } else if (screenWidth > 600) {
      baseHeight = 56;
    } else {
      baseHeight = 48;
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }
}

    final newItem = CargoItem(
      itemName: itemName,
      category: selectedCategory,
      itemType: itemType,
      weight: weight,
      quantity: quantity,
      durability: selectedDurability,
      imagePaths: selectedImages,
      canEdit: true,
      canDelete: true,
    );

    // Close dialog first
    Navigator.of(context).pop();

    // Update the main widget state
    setState(() {
      if (existingItem != null && index >= 0) {
        _cargoItems[index] = newItem;
      } else {
        _cargoItems.add(newItem);
      }
    });

    Toast.success(existingItem != null ? 'Cargo item updated' : 'Cargo item added');
  }
}
