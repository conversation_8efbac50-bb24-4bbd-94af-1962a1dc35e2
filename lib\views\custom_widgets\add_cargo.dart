import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'dart:io';

/// Durability options for cargo items
enum ItemDurability {
  fragile('Fragile'),
  average('Average'),
  durable('Durable');

  const ItemDurability(this.displayName);
  final String displayName;
}

/// Cargo Item Data Model
class CargoItem {
  final String itemName;
  final String category;
  final String itemType;
  final double weight; // Weight in kg
  final int quantity;
  final ItemDurability durability;
  final List<String> imagePaths; // Paths to item images
  final bool canEdit;
  final bool canDelete;

  const CargoItem({
    required this.itemName,
    required this.category,
    required this.itemType,
    required this.weight,
    required this.quantity,
    required this.durability,
    this.imagePaths = const [],
    this.canEdit = true,
    this.canDelete = true,
  });
}

/// A reusable add cargo widget
///
/// This widget displays cargo items in a table format with options to
/// switch delivery method and duplicate orders. Features responsive design
/// and consistent styling with the app theme.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - Customizable cargo items list
/// - Action buttons for switching delivery method and duplicating orders
/// - Clean table design with header and data rows
/// - Consistent styling with app theme
class AddCargo extends StatelessWidget {
  /// List of cargo items to display
  final List<CargoItem> cargoItems;

  /// Callback when switch delivery method is tapped
  final VoidCallback? onSwitchDeliveryMethod;

  /// Callback when duplicate order is tapped
  final VoidCallback? onDuplicateOrder;

  /// Callback when edit item is tapped
  final Function(int index)? onEditItem;

  /// Callback when delete item is tapped
  final Function(int index)? onDeleteItem;

  /// Custom title for the switch delivery method button
  final String switchDeliveryMethodText;

  /// Custom title for the duplicate order button
  final String duplicateOrderText;

  /// Custom background color for the card
  final Color? backgroundColor;

  /// Custom horizontal padding
  final double? horizontalPadding;

  /// Whether to show the card shadow
  final bool showShadow;

  /// Whether to show action buttons
  final bool showActionButtons;

  const AddCargo({
    super.key,
    required this.cargoItems,
    this.onSwitchDeliveryMethod,
    this.onDuplicateOrder,
    this.onEditItem,
    this.onDeleteItem,
    this.switchDeliveryMethodText = 'Switch Delivery Method',
    this.duplicateOrderText = 'Duplicate Order',
    this.backgroundColor,
    this.horizontalPadding,
    this.showShadow = true,
    this.showActionButtons = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding ?? _getMinimalHorizontalPadding(context),
      ),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.white,
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          boxShadow: showShadow ? [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: _getSpacing(context, 8),
              offset: Offset(0, _getSpacing(context, 2)),
              spreadRadius: 0,
            ),
          ] : null,
        ),
        child: Column(
          children: [
            // Cargo items
            if (cargoItems.isNotEmpty) ...[
              _buildCargoItems(context),
            ] else ...[
              _buildEmptyState(context),
            ],

            // Action buttons
            if (showActionButtons) ...[
              _buildActionButtons(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCargoItems(BuildContext context) {
    return Column(
      children: cargoItems.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        return _buildCargoContainer(context, item, index);
      }).toList(),
    );
  }

  Widget _buildCargoContainer(BuildContext context, CargoItem item, int index) {
    return Container(
      margin: EdgeInsets.only(
        bottom: index < cargoItems.length - 1 ? _getSpacing(context, 16) : 0,
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context)),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main cargo content
          Expanded(
            child: Column(
              children: [
                // First section: Item name, Categories, Item Type with purple header
                _buildFirstSection(context, item),

                // Second section: Weight, Quantity, Durability
                _buildSecondSection(context, item),
              ],
            ),
          ),

          // Actions column (Edit and Delete)
          if (item.canEdit || item.canDelete)
            _buildActionsColumn(context, item, index),
        ],
      ),
    );
  }

  Widget _buildFirstSection(BuildContext context, CargoItem item) {
    return Column(
      children: [
        // Purple header
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: _getContentPadding(context),
            vertical: _getSpacing(context, 12),
          ),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(_getBorderRadius(context)),
              topRight: Radius.circular(_getBorderRadius(context)),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  'Item name',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: _getHeaderFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  'Category',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: _getHeaderFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'Item Type',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: _getHeaderFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        // White background with values
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: _getContentPadding(context),
            vertical: _getSpacing(context, 12),
          ),
          decoration: BoxDecoration(
            color: AppColors.white,
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  item.itemName,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  item.category,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  item.itemType,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  Widget _buildSecondSection(BuildContext context, CargoItem item) {
    return Column(
      children: [
        // Purple header for weight, quantity, durability
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: _getContentPadding(context),
            vertical: _getSpacing(context, 12),
          ),
          decoration: BoxDecoration(
            color: AppColors.primary,
            border: Border(
              top: BorderSide(
                color: AppColors.black.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  'Weight (kg)',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: _getHeaderFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  'Quantity',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: _getHeaderFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'Durability',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: _getHeaderFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        // White background with values
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: _getContentPadding(context),
            vertical: _getSpacing(context, 12),
          ),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(_getBorderRadius(context)),
              bottomRight: Radius.circular(_getBorderRadius(context)),
            ),
          ),
          child: Column(
            children: [
              // Values for weight, quantity, durability
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Text(
                      '${item.weight.toStringAsFixed(1)} kg',
                      style: TextStyle(
                        color: AppColors.black,
                        fontSize: _getBodyFontSize(context),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      '${item.quantity}',
                      style: TextStyle(
                        color: AppColors.black,
                        fontSize: _getBodyFontSize(context),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: _getSpacing(context, 8),
                        vertical: _getSpacing(context, 2),
                      ),
                      decoration: BoxDecoration(
                        color: _getDurabilityColor(item.durability).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(_getSpacing(context, 4)),
                      ),
                      child: Text(
                        item.durability.displayName,
                        style: TextStyle(
                          color: _getDurabilityColor(item.durability),
                          fontSize: _getBodyFontSize(context) - 1,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              // Image preview section
              if (item.imagePaths.isNotEmpty) ...[
                SizedBox(height: _getSpacing(context, 12)),
                _buildImagePreview(context, item.imagePaths),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionsColumn(BuildContext context, CargoItem item, int index) {
    return Container(
      width: _getSpacing(context, 60),
      padding: EdgeInsets.symmetric(
        horizontal: _getSpacing(context, 8),
        vertical: _getSpacing(context, 16),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (item.canDelete)
            GestureDetector(
              onTap: () {
                if (onDeleteItem != null) {
                  onDeleteItem!(index);
                }
              },
              child: Container(
                padding: EdgeInsets.all(_getSpacing(context, 8)),
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                child: Icon(
                  Icons.delete,
                  color: AppColors.error,
                  size: _getIconSize(context, 20),
                ),
              ),
            ),

          if (item.canEdit && item.canDelete)
            SizedBox(height: _getSpacing(context, 12)),

          if (item.canEdit)
            GestureDetector(
              onTap: () {
                if (onEditItem != null) {
                  onEditItem!(index);
                }
              },
              child: Container(
                padding: EdgeInsets.all(_getSpacing(context, 8)),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                child: Icon(
                  Icons.edit,
                  color: AppColors.primary,
                  size: _getIconSize(context, 20),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePreview(BuildContext context, List<String> imagePaths) {
    return Container(
      height: _getSpacing(context, 60),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: imagePaths.length,
        itemBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.only(right: _getSpacing(context, 8)),
            width: _getSpacing(context, 60),
            height: _getSpacing(context, 60),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(_getSpacing(context, 8)),
              border: Border.all(
                color: AppColors.black.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(_getSpacing(context, 8)),
              child: Image.file(
                File(imagePaths[index]),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: AppColors.black.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.image_not_supported,
                      color: AppColors.black.withValues(alpha: 0.4),
                      size: _getSpacing(context, 24),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getContentPadding(context)),
      child: Column(
        children: [
          SizedBox(height: _getSpacing(context, 20)),
          Icon(
            Icons.inventory_2_outlined,
            size: _getIconSize(context, 48),
            color: AppColors.black.withValues(alpha: 0.4),
          ),
          SizedBox(height: _getSpacing(context, 12)),
          Text(
            'No cargo items added',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.6),
              fontSize: _getBodyFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: _getSpacing(context, 4)),
          Text(
            'Add items to your cargo list',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.5),
              fontSize: _getBodyFontSize(context) - 2,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: _getSpacing(context, 20)),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: _getSpacing(context, 16)),
      padding: EdgeInsets.all(_getContentPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context)),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Switch Delivery Method button
          Flexible(
            flex: 2,
            child: GestureDetector(
              onTap: onSwitchDeliveryMethod,
              child: Text(
                switchDeliveryMethodText,
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: _getBodyFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.underline,
                  decorationColor: AppColors.primary,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          SizedBox(width: _getSpacing(context, 8)),

          // Duplicate Order button
          Flexible(
            flex: 1,
            child: GestureDetector(
              onTap: onDuplicateOrder,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 12),
                  vertical: _getSpacing(context, 8),
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                child: Text(
                  duplicateOrderText,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: _getBodyFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get durability color
  Color _getDurabilityColor(ItemDurability durability) {
    switch (durability) {
      case ItemDurability.fragile:
        return AppColors.error;
      case ItemDurability.average:
        return AppColors.warning ?? Colors.orange;
      case ItemDurability.durable:
        return AppColors.success;
    }
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getReducedHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 8; // Smartwatch - reduced
    } else if (screenWidth > 600) {
      basePadding = 16; // Tablet - reduced
    } else {
      basePadding = 12; // Mobile - reduced
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getMinimalHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 4; // Smartwatch - minimal
    } else if (screenWidth > 600) {
      basePadding = 8; // Tablet - minimal
    } else {
      basePadding = 6; // Mobile - minimal
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getContentPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 16; // Smartwatch - smaller padding
    } else if (screenWidth > 600) {
      return 28; // Tablet
    } else {
      return 24; // Mobile - standard padding
    }
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch - reduced spacing
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.0; // Tablet
    } else {
      spacing = baseSpacing; // Mobile - standard spacing
    }

    if (isShortScreen) {
      spacing = spacing * 0.8; // Reduce for short screens
    }

    return spacing;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 16; // Smartwatch
    } else if (screenWidth > 600) {
      return 20; // Tablet
    } else {
      return 18; // Mobile
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6; // Smartwatch
    } else if (screenWidth > 600) {
      return 8; // Tablet
    } else {
      return 8; // Mobile
    }
  }

  double _getHeaderFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch - smaller text
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet - larger text
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch - very small text
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet - larger text
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseSize * 0.8; // Smartwatch - smaller icons
    } else if (screenWidth > 600) {
      return baseSize * 1.2; // Tablet - larger icons
    } else {
      return baseSize; // Mobile - standard size
    }
  }
}