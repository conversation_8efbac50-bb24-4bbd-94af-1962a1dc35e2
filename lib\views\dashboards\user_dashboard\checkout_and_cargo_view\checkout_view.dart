import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/views/custom_widgets/payment_summary.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Final Checkout view page for completing payment
///
/// This page displays all orders with their total cost and important information.
/// Users can review everything before completing the payment process.
///
/// Features:
/// - Display all orders with details
/// - Show total cost calculation
/// - Payment processing
/// - Order confirmation
/// - Responsive design for mobile, tablet, and smartwatch
/// - Consistent styling with app theme
class CheckoutView extends StatefulWidget {
  const CheckoutView({super.key});

  @override
  State<CheckoutView> createState() => _CheckoutViewState();
}

class _CheckoutViewState extends State<CheckoutView> {
  Map<String, dynamic> _currentOrder = {};
  VoidCallback? _onNavigateToHome;
  bool _isProcessingPayment = false;

  @override
  void initState() {
    super.initState();
    // Get arguments passed from previous screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (args != null) {
        setState(() {
          _currentOrder = args['currentOrder'] ?? {};
          _onNavigateToHome = args['onNavigateToHome'];
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(context),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(_getHorizontalPadding(context)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: _getSpacing(context, 24)),

                    // Order Summary Section
                    _buildOrderSummarySection(context),

                    SizedBox(height: _getSpacing(context, 24)),

                    // Payment Information Section
                    _buildPaymentSection(context),

                    SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom buttons
                  ],
                ),
              ),
            ),

            // Bottom Action Buttons
            _buildBottomActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: _getIconSize(context, 40),
              height: _getIconSize(context, 40),
              decoration: BoxDecoration(
                color: AppColors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                size: _getIconSize(context, 20),
                color: AppColors.black,
              ),
            ),
          ),
          SizedBox(width: _getSpacing(context, 16)),

          // Title
          Expanded(
            child: Text(
              'Checkout',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getTitleFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
                letterSpacing: -0.5,
              ),
            ),
          ),

          // Security badge
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 12),
              vertical: _getSpacing(context, 6),
            ),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
              border: Border.all(
                color: AppColors.success.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.security,
                  color: AppColors.success,
                  size: _getIconSize(context, 16),
                ),
                SizedBox(width: _getSpacing(context, 4)),
                Text(
                  'Secure',
                  style: TextStyle(
                    color: AppColors.success,
                    fontSize: _getBodyFontSize(context) - 1,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummarySection(BuildContext context) {
    final pickupData = _currentOrder['pickupData'] as Map<String, dynamic>? ?? {};
    final receiverData = _currentOrder['receiverData'] as Map<String, dynamic>? ?? {};
    final cargoItems = _currentOrder['cargoItems'] as List<dynamic>? ?? [];
    final trackingNumber = _currentOrder['trackingNumber'] ?? 'N/A';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Order Summary',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getTitleFontSize(context) - 2,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),

        // Order details card
        Container(
          padding: EdgeInsets.all(_getSpacing(context, 20)),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order number
              Row(
                children: [
                  Icon(
                    Icons.receipt_long,
                    color: AppColors.primary,
                    size: _getIconSize(context, 24),
                  ),
                  SizedBox(width: _getSpacing(context, 12)),
                  Expanded(
                    child: Text(
                      'Order #$trackingNumber',
                      style: TextStyle(
                        color: AppColors.black,
                        fontSize: _getBodyFontSize(context) + 2,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: _getSpacing(context, 20)),

              // Pickup and Delivery locations
              Row(
                children: [
                  // Pickup location
                  Expanded(
                    child: _buildLocationSummary(
                      context,
                      'Pickup',
                      pickupData['senderName'] ?? 'N/A',
                      pickupData['fullAddress'] ?? 'N/A',
                      Icons.send,
                      AppColors.primary,
                    ),
                  ),
                  SizedBox(width: _getSpacing(context, 16)),
                  // Arrow
                  Icon(
                    Icons.arrow_forward,
                    color: AppColors.black.withValues(alpha: 0.4),
                    size: _getIconSize(context, 20),
                  ),
                  SizedBox(width: _getSpacing(context, 16)),
                  // Delivery location
                  Expanded(
                    child: _buildLocationSummary(
                      context,
                      'Delivery',
                      receiverData['name'] ?? 'N/A',
                      receiverData['address'] ?? 'N/A',
                      Icons.location_on,
                      AppColors.success,
                    ),
                  ),
                ],
              ),

              SizedBox(height: _getSpacing(context, 20)),

              // Cargo items summary
              if (cargoItems.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.inventory_2,
                      color: AppColors.black.withValues(alpha: 0.6),
                      size: _getIconSize(context, 20),
                    ),
                    SizedBox(width: _getSpacing(context, 8)),
                    Text(
                      '${cargoItems.length} cargo items',
                      style: TextStyle(
                        color: AppColors.black.withValues(alpha: 0.8),
                        fontSize: _getBodyFontSize(context),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: _getSpacing(context, 12)),
                _buildCargoItemsList(context, cargoItems),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCargoItemsList(BuildContext context, List<dynamic> cargoItems) {
    final items = cargoItems.map((item) {
      final itemMap = item as Map<String, dynamic>;
      return CargoItem(
        itemName: itemMap['itemName'] ?? 'Unknown Item',
        category: itemMap['category'] ?? 'General',
        itemType: itemMap['itemType'] ?? 'Package',
        weight: (itemMap['weight'] as num?)?.toDouble() ?? 0.0,
        quantity: (itemMap['quantity'] as num?)?.toInt() ?? 1,
        durability: _parseDurability(itemMap['durability']) ?? ItemDurability.average,
        imagePaths: List<String>.from(itemMap['imagePaths'] ?? []),
        canEdit: false,
        canDelete: false,
      );
    }).toList();

    return AddCargo(
      cargoItems: items,
      showShadow: false,
      showActionButtons: false,
      horizontalPadding: 0,
    );
  }

  Widget _buildPaymentSection(BuildContext context) {
    final paymentData = _currentOrder['paymentData'] as Map<String, dynamic>? ?? {};

    if (paymentData.isEmpty) {
      return SizedBox.shrink();
    }

    final paymentSummaryData = PaymentSummaryData(
      shippingCost: (paymentData['shippingCost'] as num?)?.toDouble() ?? 0.0,
      vat: (paymentData['vat'] as num?)?.toDouble() ?? 0.0,
      insurance: (paymentData['insurance'] as num?)?.toDouble() ?? 0.0,
      pickupCharge: (paymentData['pickupCharge'] as num?)?.toDouble() ?? 0.0,
      isInsuranceFree: paymentData['isInsuranceFree'] ?? true,
      isPickupFree: paymentData['isPickupFree'] ?? true,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Payment Details',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getTitleFontSize(context) - 2,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),

        // Payment summary
        PaymentSummary(
          paymentData: paymentSummaryData,
          title: 'Total Amount',
          showShadow: true,
        ),

        SizedBox(height: _getSpacing(context, 16)),

        // Payment method info
        _buildPaymentMethodInfo(context),
      ],
    );
  }

  Widget _buildPaymentMethodInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppColors.primary,
            size: _getIconSize(context, 24),
          ),
          SizedBox(width: _getSpacing(context, 12)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Payment Method',
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context) + 1,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  'Payment will be processed securely. You can pay with cash on delivery or through our secure payment gateway.',
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.7),
                    fontSize: _getBodyFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    final paymentData = _currentOrder['paymentData'] as Map<String, dynamic>? ?? {};
    final total = (paymentData['total'] as num?)?.toDouble() ?? 0.0;

    return Container(
      padding: EdgeInsets.all(_getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Total amount display
          Container(
            padding: EdgeInsets.all(_getSpacing(context, 16)),
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.02),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              border: Border.all(
                color: AppColors.black.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Amount',
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context) + 2,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '₦${total.toStringAsFixed(2)}',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: _getBodyFontSize(context) + 4,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Action buttons
          Row(
            children: [
              // Cancel button
              Expanded(
                child: OutlinedButton(
                  onPressed: _isProcessingPayment ? null : () => Navigator.of(context).pop(),
                  style: OutlinedButton.styleFrom(
                    backgroundColor: AppColors.white,
                    foregroundColor: AppColors.black.withValues(alpha: 0.7),
                    side: BorderSide(
                      width: 1.5,
                      color: AppColors.black.withValues(alpha: 0.2),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                    ),
                    minimumSize: Size(0, _getButtonHeight(context)),
                  ),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      fontSize: _getButtonFontSize(context),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),

              SizedBox(width: _getSpacing(context, 12)),

              // Complete Payment button
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _isProcessingPayment ? null : _handleCompletePayment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                    ),
                    minimumSize: Size(0, _getButtonHeight(context)),
                    elevation: 0,
                  ),
                  child: _isProcessingPayment
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Complete Payment',
                          style: TextStyle(
                            fontSize: _getButtonFontSize(context),
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSummary(
    BuildContext context,
    String title,
    String name,
    String address,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: _getIconSize(context, 20),
              height: _getIconSize(context, 20),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 4)),
              ),
              child: Icon(
                icon,
                color: color,
                size: _getIconSize(context, 12),
              ),
            ),
            SizedBox(width: _getSpacing(context, 8)),
            Text(
              title,
              style: TextStyle(
                color: AppColors.black.withValues(alpha: 0.6),
                fontSize: _getBodyFontSize(context) - 1,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: _getSpacing(context, 8)),
        Text(
          name,
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: _getSpacing(context, 4)),
        Text(
          address,
          style: TextStyle(
            color: AppColors.black.withValues(alpha: 0.7),
            fontSize: _getBodyFontSize(context) - 1,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w400,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  // Event handlers
  void _handleCompletePayment() async {
    setState(() {
      _isProcessingPayment = true;
    });

    try {
      // Simulate payment processing
      await Future.delayed(Duration(seconds: 3));

      // Save order data locally
      await _saveOrderData();

      // Show success message
      Toast.success('Payment successful! Order placed.');

      // Wait a moment for user to see success message
      await Future.delayed(Duration(milliseconds: 1500));

      // Return success result and navigate back to home
      Navigator.of(context).pop('payment_success');

    } catch (e) {
      // Show error message
      Toast.error('Payment failed. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingPayment = false;
        });
      }
    }
  }

  Future<void> _saveOrderData() async {
    try {
      // Save as completed order
      await PackageDataService.saveCompletedOrder(_currentOrder);

      // Also save as current shipment (for tracking)
      await PackageDataService.saveCurrentShipment(_currentOrder);

    } catch (e) {
      // Don't throw error to prevent payment flow interruption
      print('Error saving order data: $e');
    }
  }

  // Utility methods
  ItemDurability? _parseDurability(dynamic durabilityValue) {
    if (durabilityValue == null) return null;

    final durabilityString = durabilityValue.toString().toLowerCase();
    switch (durabilityString) {
      case 'fragile':
        return ItemDurability.fragile;
      case 'average':
        return ItemDurability.average;
      case 'durable':
        return ItemDurability.durable;
      default:
        return null;
    }
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16;
    if (screenWidth > 600) return 40;
    return 24;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getBorderRadius(BuildContext context, double baseRadius) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseRadius * 0.8;
    } else if (screenWidth > 600) {
      return baseRadius * 1.2;
    } else {
      return baseRadius;
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6;
    } else if (screenWidth > 600) {
      return 10;
    } else {
      return 8;
    }
  }

  double _getIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseSize * 0.8;
    } else if (screenWidth > 600) {
      return baseSize * 1.2;
    } else {
      return baseSize;
    }
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 16;
    } else if (screenWidth > 600) {
      baseSize = 24;
    } else {
      baseSize = 20;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10;
    } else if (screenWidth > 600) {
      baseSize = 16;
    } else {
      baseSize = 14;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12;
    } else if (screenWidth > 600) {
      baseSize = 18;
    } else {
      baseSize = 16;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40;
    } else if (screenWidth > 600) {
      baseHeight = 56;
    } else {
      baseHeight = 48;
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }
}
