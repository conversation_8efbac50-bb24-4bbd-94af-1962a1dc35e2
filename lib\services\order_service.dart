import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Service class for managing order data
///
/// This service handles storing, retrieving, and managing order information
/// including completed orders, draft orders, and order counts.
///
/// Features:
/// - Store and retrieve completed orders
/// - Manage draft orders
/// - Get order counts for UI badges
/// - Clear order data when needed
class OrderService {
  static const String _completedOrdersKey = 'completed_orders';
  static const String _draftOrdersKey = 'draft_orders';
  static const String _orderCountKey = 'order_count';

  /// Get all completed orders
  static Future<List<Map<String, dynamic>>> getCompletedOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getString(_completedOrdersKey);
      
      if (ordersJson != null) {
        final List<dynamic> ordersList = json.decode(ordersJson);
        return ordersList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      print('Error getting completed orders: $e');
      return [];
    }
  }

  /// Save a completed order
  static Future<bool> saveCompletedOrder(Map<String, dynamic> orderData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingOrders = await getCompletedOrders();
      
      // Add timestamp if not present
      if (!orderData.containsKey('timestamp')) {
        orderData['timestamp'] = DateTime.now().millisecondsSinceEpoch;
      }
      
      // Add order ID if not present
      if (!orderData.containsKey('orderId')) {
        orderData['orderId'] = _generateOrderId();
      }
      
      existingOrders.add(orderData);
      
      final ordersJson = json.encode(existingOrders);
      await prefs.setString(_completedOrdersKey, ordersJson);
      
      // Update order count
      await _updateOrderCount(existingOrders.length);
      
      return true;
    } catch (e) {
      print('Error saving completed order: $e');
      return false;
    }
  }

  /// Get all draft orders
  static Future<List<Map<String, dynamic>>> getDraftOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getString(_draftOrdersKey);
      
      if (ordersJson != null) {
        final List<dynamic> ordersList = json.decode(ordersJson);
        return ordersList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      print('Error getting draft orders: $e');
      return [];
    }
  }

  /// Save a draft order
  static Future<bool> saveDraftOrder(Map<String, dynamic> orderData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingDrafts = await getDraftOrders();
      
      // Add timestamp if not present
      if (!orderData.containsKey('timestamp')) {
        orderData['timestamp'] = DateTime.now().millisecondsSinceEpoch;
      }
      
      // Add draft ID if not present
      if (!orderData.containsKey('draftId')) {
        orderData['draftId'] = _generateOrderId();
      }
      
      existingDrafts.add(orderData);
      
      final ordersJson = json.encode(existingDrafts);
      await prefs.setString(_draftOrdersKey, ordersJson);
      
      return true;
    } catch (e) {
      print('Error saving draft order: $e');
      return false;
    }
  }

  /// Get total order count (completed + draft)
  static Future<int> getTotalOrderCount() async {
    try {
      final completedOrders = await getCompletedOrders();
      final draftOrders = await getDraftOrders();
      return completedOrders.length + draftOrders.length;
    } catch (e) {
      print('Error getting total order count: $e');
      return 0;
    }
  }

  /// Get completed order count only
  static Future<int> getCompletedOrderCount() async {
    try {
      final completedOrders = await getCompletedOrders();
      return completedOrders.length;
    } catch (e) {
      print('Error getting completed order count: $e');
      return 0;
    }
  }

  /// Get draft order count only
  static Future<int> getDraftOrderCount() async {
    try {
      final draftOrders = await getDraftOrders();
      return draftOrders.length;
    } catch (e) {
      print('Error getting draft order count: $e');
      return 0;
    }
  }

  /// Delete a completed order by ID
  static Future<bool> deleteCompletedOrder(String orderId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingOrders = await getCompletedOrders();
      
      existingOrders.removeWhere((order) => order['orderId'] == orderId);
      
      final ordersJson = json.encode(existingOrders);
      await prefs.setString(_completedOrdersKey, ordersJson);
      
      // Update order count
      await _updateOrderCount(existingOrders.length);
      
      return true;
    } catch (e) {
      print('Error deleting completed order: $e');
      return false;
    }
  }

  /// Delete a draft order by ID
  static Future<bool> deleteDraftOrder(String draftId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingDrafts = await getDraftOrders();
      
      existingDrafts.removeWhere((draft) => draft['draftId'] == draftId);
      
      final ordersJson = json.encode(existingDrafts);
      await prefs.setString(_draftOrdersKey, ordersJson);
      
      return true;
    } catch (e) {
      print('Error deleting draft order: $e');
      return false;
    }
  }

  /// Clear all completed orders
  static Future<bool> clearCompletedOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_completedOrdersKey);
      await _updateOrderCount(0);
      return true;
    } catch (e) {
      print('Error clearing completed orders: $e');
      return false;
    }
  }

  /// Clear all draft orders
  static Future<bool> clearDraftOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_draftOrdersKey);
      return true;
    } catch (e) {
      print('Error clearing draft orders: $e');
      return false;
    }
  }

  /// Clear all order data
  static Future<bool> clearAllOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_completedOrdersKey);
      await prefs.remove(_draftOrdersKey);
      await prefs.remove(_orderCountKey);
      return true;
    } catch (e) {
      print('Error clearing all orders: $e');
      return false;
    }
  }

  /// Get order by ID from completed orders
  static Future<Map<String, dynamic>?> getCompletedOrderById(String orderId) async {
    try {
      final orders = await getCompletedOrders();
      for (final order in orders) {
        if (order['orderId'] == orderId) {
          return order;
        }
      }
      return null;
    } catch (e) {
      print('Error getting order by ID: $e');
      return null;
    }
  }

  /// Get draft by ID from draft orders
  static Future<Map<String, dynamic>?> getDraftOrderById(String draftId) async {
    try {
      final drafts = await getDraftOrders();
      for (final draft in drafts) {
        if (draft['draftId'] == draftId) {
          return draft;
        }
      }
      return null;
    } catch (e) {
      print('Error getting draft by ID: $e');
      return null;
    }
  }

  /// Update order count in preferences
  static Future<void> _updateOrderCount(int count) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_orderCountKey, count);
    } catch (e) {
      print('Error updating order count: $e');
    }
  }

  /// Generate a unique order ID
  static String _generateOrderId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'ORD${timestamp.toString().substring(8)}$random';
  }

  /// Convert order to tracking shipment format
  static Map<String, dynamic> orderToShipment(Map<String, dynamic> orderData) {
    final pickupData = orderData['pickupData'] as Map<String, dynamic>? ?? {};
    final receiverData = orderData['receiverData'] as Map<String, dynamic>? ?? {};
    final paymentData = orderData['paymentData'] as Map<String, dynamic>? ?? {};
    
    return {
      'trackingNumber': orderData['trackingNumber'] ?? orderData['orderId'] ?? 'N/A',
      'status': orderData['status'] ?? 'pending',
      'senderName': pickupData['senderName'] ?? 'N/A',
      'receiverName': receiverData['name'] ?? 'N/A',
      'pickupLocation': pickupData['fullAddress'] ?? 'N/A',
      'deliveryLocation': receiverData['address'] ?? 'N/A',
      'estimatedDelivery': _calculateEstimatedDelivery(orderData),
      'cost': (paymentData['total'] as num?)?.toDouble() ?? 0.0,
      'orderDate': orderData['orderDate'] ?? DateTime.now().toIso8601String(),
      'isExpanded': false,
    };
  }

  /// Calculate estimated delivery time
  static String _calculateEstimatedDelivery(Map<String, dynamic> orderData) {
    try {
      final orderDateString = orderData['orderDate'] as String?;
      if (orderDateString != null) {
        final orderDate = DateTime.parse(orderDateString);
        final estimatedDelivery = orderDate.add(Duration(days: 2)); // 2 days delivery
        return estimatedDelivery.toIso8601String();
      }
    } catch (e) {
      print('Error calculating estimated delivery: $e');
    }
    
    // Default to 2 days from now
    return DateTime.now().add(Duration(days: 2)).toIso8601String();
  }
}
