import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/services/package_data_service.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/send_package_view.dart';

/// Orders Summary view page for viewing all user orders
///
/// This page displays all orders with their pickup locations, delivery locations,
/// and cargo items. Users can view order details and manage their orders.
///
/// Features:
/// - View all orders with details
/// - Expandable order cards showing pickup/delivery locations
/// - Cargo items display for each order
/// - Order status indicators
/// - Responsive design for mobile, tablet, and smartwatch
/// - Consistent styling with app theme
class OrdersSummaryView extends StatefulWidget {
  const OrdersSummaryView({super.key});

  @override
  State<OrdersSummaryView> createState() => _OrdersSummaryViewState();
}

class _OrdersSummaryViewState extends State<OrdersSummaryView> {
  List<Map<String, dynamic>> _orders = [];
  bool _isLoading = true;
  Set<int> _expandedOrders = {};

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load completed orders from PackageDataService
      final orders = await PackageDataService.getCompletedOrders();
      setState(() {
        _orders = orders;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      Toast.error('Failed to load orders');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(context),

            // Content
            Expanded(
              child: _isLoading
                  ? _buildLoadingState(context)
                  : _orders.isEmpty
                      ? _buildEmptyState(context)
                      : _buildOrdersList(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: _getIconSize(context, 40),
              height: _getIconSize(context, 40),
              decoration: BoxDecoration(
                color: AppColors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                size: _getIconSize(context, 20),
                color: AppColors.black,
              ),
            ),
          ),
          SizedBox(width: _getSpacing(context, 16)),

          // Title
          Expanded(
            child: Text(
              'All Orders',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getTitleFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
                letterSpacing: -0.5,
              ),
            ),
          ),

          // Orders count badge
          if (_orders.isNotEmpty)
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: _getSpacing(context, 12),
                vertical: _getSpacing(context, 6),
              ),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
              ),
              child: Text(
                '${_orders.length}',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: _getBodyFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            'Loading orders...',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.6),
              fontSize: _getBodyFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(_getHorizontalPadding(context)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: _getIconSize(context, 80),
              color: AppColors.black.withValues(alpha: 0.4),
            ),
            SizedBox(height: _getSpacing(context, 24)),
            Text(
              'No Orders Yet',
              style: TextStyle(
                color: AppColors.black.withValues(alpha: 0.8),
                fontSize: _getTitleFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: _getSpacing(context, 12)),
            Text(
              'You haven\'t placed any orders yet. Start by sending a package to create your first order.',
              style: TextStyle(
                color: AppColors.black.withValues(alpha: 0.6),
                fontSize: _getBodyFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: _getSpacing(context, 32)),
            ElevatedButton(
              onPressed: () {
                // Navigate back and then to send package view
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SendPackageView(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                minimumSize: Size(200, _getButtonHeight(context)),
              ),
              child: Text(
                'Send a Package',
                style: TextStyle(
                  fontSize: _getButtonFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersList(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadOrders,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(_getHorizontalPadding(context)),
        itemCount: _orders.length,
        itemBuilder: (context, index) {
          final order = _orders[index];
          final isExpanded = _expandedOrders.contains(index);
          
          return _buildOrderCard(context, order, index, isExpanded);
        },
      ),
    );
  }

  Widget _buildOrderCard(BuildContext context, Map<String, dynamic> order, int index, bool isExpanded) {
    final trackingNumber = order['trackingNumber'] ?? 'N/A';
    final orderDate = order['orderDate'] ?? '';
    final status = order['status'] ?? 'pending';
    final cargoItems = order['cargoItems'] as List<dynamic>? ?? [];
    
    return Container(
      margin: EdgeInsets.only(bottom: _getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // Order header
          GestureDetector(
            onTap: () => _toggleOrderExpansion(index),
            child: Container(
              padding: EdgeInsets.all(_getSpacing(context, 16)),
              child: Row(
                children: [
                  // Status indicator
                  Container(
                    width: _getIconSize(context, 12),
                    height: _getIconSize(context, 12),
                    decoration: BoxDecoration(
                      color: _getStatusColor(status),
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  SizedBox(width: _getSpacing(context, 12)),

                  // Order info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order #$trackingNumber',
                          style: TextStyle(
                            color: AppColors.black,
                            fontSize: _getBodyFontSize(context) + 1,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: _getSpacing(context, 4)),
                        Text(
                          _formatDate(orderDate),
                          style: TextStyle(
                            color: AppColors.black.withValues(alpha: 0.6),
                            fontSize: _getBodyFontSize(context) - 1,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Items count
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: _getSpacing(context, 8),
                      vertical: _getSpacing(context, 4),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                    ),
                    child: Text(
                      '${cargoItems.length} items',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: _getBodyFontSize(context) - 2,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  SizedBox(width: _getSpacing(context, 8)),

                  // Expand/collapse icon
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: AppColors.black.withValues(alpha: 0.6),
                    size: _getIconSize(context, 24),
                  ),
                ],
              ),
            ),
          ),

          // Expanded content
          if (isExpanded) ...[
            Divider(
              height: 1,
              color: AppColors.black.withValues(alpha: 0.1),
            ),
            _buildOrderDetails(context, order),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context, Map<String, dynamic> order) {
    final pickupData = order['pickupData'] as Map<String, dynamic>? ?? {};
    final receiverData = order['receiverData'] as Map<String, dynamic>? ?? {};
    final cargoItems = order['cargoItems'] as List<dynamic>? ?? [];
    final paymentData = order['paymentData'] as Map<String, dynamic>? ?? {};

    return Padding(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Pickup and Delivery Locations
          Row(
            children: [
              // Pickup location
              Expanded(
                child: _buildLocationCard(
                  context,
                  'Pickup Location',
                  pickupData,
                  Icons.send,
                  AppColors.primary,
                ),
              ),
              SizedBox(width: _getSpacing(context, 12)),
              // Delivery location
              Expanded(
                child: _buildLocationCard(
                  context,
                  'Delivery Location',
                  receiverData,
                  Icons.location_on,
                  AppColors.success,
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Cargo Items
          if (cargoItems.isNotEmpty) ...[
            Text(
              'Cargo Items (${cargoItems.length})',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getBodyFontSize(context) + 1,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: _getSpacing(context, 12)),
            _buildCargoItemsList(context, cargoItems),
            SizedBox(height: _getSpacing(context, 16)),
          ],

          // Payment Summary
          if (paymentData.isNotEmpty) ...[
            _buildPaymentSummary(context, paymentData),
          ],
        ],
      ),
    );
  }

  Widget _buildLocationCard(
    BuildContext context,
    String title,
    Map<String, dynamic> locationData,
    IconData icon,
    Color color,
  ) {
    final isPickup = title == 'Pickup Location';
    final name = isPickup
        ? (locationData['senderName'] ?? 'N/A')
        : (locationData['name'] ?? 'N/A');
    final address = isPickup
        ? (locationData['fullAddress'] ?? 'N/A')
        : (locationData['address'] ?? 'N/A');

    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 12)),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: _getIconSize(context, 24),
                height: _getIconSize(context, 24),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 6)),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: _getIconSize(context, 14),
                ),
              ),
              SizedBox(width: _getSpacing(context, 8)),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context) - 1,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            name,
            style: TextStyle(
              color: AppColors.black,
              fontSize: _getBodyFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: _getSpacing(context, 4)),
          Text(
            address,
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.7),
              fontSize: _getBodyFontSize(context) - 1,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildCargoItemsList(BuildContext context, List<dynamic> cargoItems) {
    final items = cargoItems.map((item) {
      final itemMap = item as Map<String, dynamic>;
      return CargoItem(
        itemName: itemMap['itemName'] ?? 'Unknown Item',
        category: itemMap['category'] ?? 'General',
        itemType: itemMap['itemType'] ?? 'Package',
        weight: (itemMap['weight'] as num?)?.toDouble() ?? 0.0,
        quantity: (itemMap['quantity'] as num?)?.toInt() ?? 1,
        durability: _parseDurability(itemMap['durability']) ?? ItemDurability.average,
        imagePaths: List<String>.from(itemMap['imagePaths'] ?? []),
        canEdit: false,
        canDelete: false,
      );
    }).toList();

    return AddCargo(
      cargoItems: items,
      showShadow: false,
      showActionButtons: false,
      horizontalPadding: 0,
    );
  }

  Widget _buildPaymentSummary(BuildContext context, Map<String, dynamic> paymentData) {
    final total = (paymentData['total'] as num?)?.toDouble() ?? 0.0;
    final shippingCost = (paymentData['shippingCost'] as num?)?.toDouble() ?? 0.0;
    final vat = (paymentData['vat'] as num?)?.toDouble() ?? 0.0;

    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 12)),
      decoration: BoxDecoration(
        color: AppColors.black.withValues(alpha: 0.02),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Summary',
            style: TextStyle(
              color: AppColors.black,
              fontSize: _getBodyFontSize(context) + 1,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          _buildPaymentRow(context, 'Shipping Cost', '₦${shippingCost.toStringAsFixed(2)}'),
          if (vat > 0) ...[
            SizedBox(height: _getSpacing(context, 4)),
            _buildPaymentRow(context, 'VAT', '₦${vat.toStringAsFixed(2)}'),
          ],
          SizedBox(height: _getSpacing(context, 8)),
          Divider(
            height: 1,
            color: AppColors.black.withValues(alpha: 0.2),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          _buildPaymentRow(
            context,
            'Total',
            '₦${total.toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(BuildContext context, String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppColors.black.withValues(alpha: isTotal ? 1.0 : 0.7),
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: isTotal ? FontWeight.w700 : FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // Event handlers and utility methods
  void _toggleOrderExpansion(int index) {
    setState(() {
      if (_expandedOrders.contains(index)) {
        _expandedOrders.remove(index);
      } else {
        _expandedOrders.add(index);
      }
    });
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'in_progress':
        return AppColors.primary;
      case 'delivered':
        return AppColors.success;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(String dateString) {
    if (dateString.isEmpty) return 'N/A';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  ItemDurability? _parseDurability(dynamic durabilityValue) {
    if (durabilityValue == null) return null;

    final durabilityString = durabilityValue.toString().toLowerCase();
    switch (durabilityString) {
      case 'fragile':
        return ItemDurability.fragile;
      case 'average':
        return ItemDurability.average;
      case 'durable':
        return ItemDurability.durable;
      default:
        return null;
    }
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16;
    if (screenWidth > 600) return 40;
    return 24;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getBorderRadius(BuildContext context, double baseRadius) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseRadius * 0.8;
    } else if (screenWidth > 600) {
      return baseRadius * 1.2;
    } else {
      return baseRadius;
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6;
    } else if (screenWidth > 600) {
      return 10;
    } else {
      return 8;
    }
  }

  double _getIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseSize * 0.8;
    } else if (screenWidth > 600) {
      return baseSize * 1.2;
    } else {
      return baseSize;
    }
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 16;
    } else if (screenWidth > 600) {
      baseSize = 24;
    } else {
      baseSize = 20;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10;
    } else if (screenWidth > 600) {
      baseSize = 16;
    } else {
      baseSize = 14;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12;
    } else if (screenWidth > 600) {
      baseSize = 18;
    } else {
      baseSize = 16;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40;
    } else if (screenWidth > 600) {
      baseHeight = 56;
    } else {
      baseHeight = 48;
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }
}
